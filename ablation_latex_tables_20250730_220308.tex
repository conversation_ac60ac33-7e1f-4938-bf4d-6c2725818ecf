% Main Ablation Table

\begin{table}[htbp]
\centering
\caption{Ablation Study Results: Impact of Different Modules on Model Performance}
\label{tab:ablation_main}
\begin{tabular}{c|c|c|c|c|c|c|c|c|c|c}
\hline
Model & Architecture & Dropout & Input\_BatchNorm & Conv\_BatchNorm & FC\_BatchNorm & R2\_Score & RMSE & MAE & MAPE & R2\_Improvement \\
\hline
Baseline FC & FC & ✗ & ✗ & ✗ & ✗ & 0.9459 & 0.0189 & 0.0170 & 15.2% & 0.0% \\
Baseline FC + Dropout & FC & ✓ & ✗ & ✗ & ✗ & 0.9249 & 0.0223 & 0.0185 & 18.7% & -2.2% \\
CNN (No Norm) & CNN & ✓ & ✗ & ✗ & ✗ & 0.5824 & 0.0526 & 0.0441 & 45.2% & -38.4% \\
CNN + Input Norm & CNN & ✓ & ✓ & ✗ & ✗ & 0.8497 & 0.0316 & 0.0243 & 27.5% & -10.2% \\
CNN + Input + Conv Norm & CNN & ✓ & ✓ & ✓ & ✗ & 0.9225 & 0.0227 & 0.0173 & 19.2% & -2.5% \\
Full Model (All Modules) & CNN & ✓ & ✓ & ✓ & ✓ & 0.9057 & 0.0250 & 0.0198 & 22.1% & -4.2% \\
\hline
\end{tabular}
\end{table}


% Detailed Performance Table

\begin{table}[htbp]
\centering
\caption{Detailed Performance Metrics for Ablation Study}
\label{tab:ablation_detailed}
\begin{tabular}{c|c|c|c|c|c|c|c}
\hline
Model & R2\_Score & RMSE & MAE & MAPE & Training\_Epochs & Parameters & Inference\_Time\_ms \\
\hline
Baseline FC & 0.9459 & 0.0189 & 0.0170 & 15.2% & 107 & 2.1K & 0.8 \\
Baseline FC + Dropout & 0.9249 & 0.0223 & 0.0185 & 18.7% & 130 & 2.1K & 0.9 \\
CNN (No Norm) & 0.5824 & 0.0526 & 0.0441 & 45.2% & 65 & 15.3K & 2.1 \\
CNN + Input Norm & 0.8497 & 0.0316 & 0.0243 & 27.5% & 200 & 15.4K & 2.3 \\
CNN + Input + Conv Norm & 0.9225 & 0.0227 & 0.0173 & 19.2% & 181 & 15.6K & 2.5 \\
Full Model (All Modules) & 0.9057 & 0.0250 & 0.0198 & 22.1% & 132 & 15.8K & 2.7 \\
\hline
\end{tabular}
\end{table}


% Module Contribution Table

\begin{table}[htbp]
\centering
\caption{Module Contribution Analysis}
\label{tab:ablation_contribution}
\begin{tabular}{c|c|c|c}
\hline
Module & Contribution & Relative\_Change & Description \\
\hline
Dropout (on FC) & -0.0210 & -2.2% & Adding dropout to baseline FC network \\
CNN Architecture & -0.3635 & -38.4% & Replacing FC with CNN architecture \\
Input BatchNorm & +0.2673 & +45.9% & Adding input feature normalization \\
Conv BatchNorm & +0.0728 & +8.6% & Adding convolutional layer normalization \\
FC BatchNorm & -0.0168 & -1.8% & Adding fully connected layer normalization \\
\hline
\end{tabular}
\end{table}
