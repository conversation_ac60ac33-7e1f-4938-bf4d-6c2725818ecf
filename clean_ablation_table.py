import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import json
import os

# 设置matplotlib支持中文
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class CleanAblationTable:
    """简洁的消融试验表格生成器 - 只保留配置名称和核心指标"""
    
    def __init__(self):
        # 简洁的消融试验数据
        self.ablation_data = [
            {
                '模型配置': '基线模型',
                'CNN预测R²': 0.0,
                'RMSE': 0.0,
                'MAE': 0.0,
                'FPS': 0.0
            },
            {
                '模型配置': 'YOLOv11分割',
                'CNN预测R²': 0.0,
                'RMSE': 0.0,
                'MAE': 0.0,
                'FPS': 13.7
            },
            {
                '模型配置': 'YOLOv11 + DeepSORT',
                'CNN预测R²': 0.0,
                'RMSE': 0.0,
                'MAE': 0.0,
                'FPS': 11.2
            },
            {
                '模型配置': 'YOLOv11 + 特征提取',
                'CNN预测R²': 0.0,
                'RMSE': 0.0,
                'MAE': 0.0,
                'FPS': 12.8
            },
            {
                '模型配置': 'YOLOv11 + CNN回归',
                'CNN预测R²': 0.9175,
                'RMSE': 0.0236,
                'MAE': 0.0178,
                'FPS': 12.1
            },
            {
                '模型配置': 'YOLOv11 + DeepSORT + CNN',
                'CNN预测R²': 0.9175,
                'RMSE': 0.0236,
                'MAE': 0.0178,
                'FPS': 9.8
            },
            {
                '模型配置': '完整智能气吹系统',
                'CNN预测R²': 0.9175,
                'RMSE': 0.0236,
                'MAE': 0.0178,
                'FPS': 9.1
            }
        ]
        
        print("📊 简洁消融试验表格生成器已初始化")
    
    def generate_main_table(self):
        """生成主要的消融试验表格"""
        df = pd.DataFrame(self.ablation_data)
        
        # 格式化数值
        df_formatted = df.copy()
        df_formatted['CNN预测R²'] = df_formatted['CNN预测R²'].apply(
            lambda x: f"{x:.4f}" if x > 0 else "N/A"
        )
        df_formatted['RMSE'] = df_formatted['RMSE'].apply(
            lambda x: f"{x:.4f}" if x > 0 else "N/A"
        )
        df_formatted['MAE'] = df_formatted['MAE'].apply(
            lambda x: f"{x:.4f}" if x > 0 else "N/A"
        )
        df_formatted['FPS'] = df_formatted['FPS'].apply(
            lambda x: f"{x:.1f}" if x > 0 else "N/A"
        )
        
        return df_formatted
    
    def generate_cnn_only_table(self):
        """生成只包含CNN配置的表格"""
        df = pd.DataFrame(self.ablation_data)
        
        # 只保留有CNN预测的配置
        cnn_configs = df[df['CNN预测R²'] > 0].copy()
        
        if len(cnn_configs) == 0:
            return pd.DataFrame()
        
        # 格式化数值
        cnn_configs['CNN预测R²'] = cnn_configs['CNN预测R²'].apply(lambda x: f"{x:.4f}")
        cnn_configs['RMSE'] = cnn_configs['RMSE'].apply(lambda x: f"{x:.4f}")
        cnn_configs['MAE'] = cnn_configs['MAE'].apply(lambda x: f"{x:.4f}")
        cnn_configs['FPS'] = cnn_configs['FPS'].apply(lambda x: f"{x:.1f}")
        
        return cnn_configs
    
    def generate_performance_comparison(self):
        """生成性能对比表格"""
        df = pd.DataFrame(self.ablation_data)
        
        comparison_data = []
        for _, row in df.iterrows():
            # 实时性评估
            real_time = "是" if row['FPS'] >= 25 else "否" if row['FPS'] > 0 else "N/A"
            
            # CNN性能等级
            if row['CNN预测R²'] > 0:
                r2_score = row['CNN预测R²']
                if r2_score >= 0.9:
                    cnn_level = "优秀"
                elif r2_score >= 0.8:
                    cnn_level = "良好"
                elif r2_score >= 0.7:
                    cnn_level = "一般"
                else:
                    cnn_level = "较差"
            else:
                cnn_level = "无CNN"
            
            # 推荐场景
            if row['CNN预测R²'] >= 0.9 and row['FPS'] >= 10:
                scenario = "生产部署"
            elif row['CNN预测R²'] >= 0.9:
                scenario = "高精度应用"
            elif row['FPS'] >= 12:
                scenario = "实时演示"
            elif row['FPS'] > 0:
                scenario = "功能验证"
            else:
                scenario = "基线对比"
            
            comparison_data.append({
                '系统配置': row['模型配置'],
                'CNN预测R²': f"{row['CNN预测R²']:.4f}" if row['CNN预测R²'] > 0 else "N/A",
                'RMSE': f"{row['RMSE']:.4f}" if row['RMSE'] > 0 else "N/A",
                'MAE': f"{row['MAE']:.4f}" if row['MAE'] > 0 else "N/A",
                'FPS': f"{row['FPS']:.1f}" if row['FPS'] > 0 else "N/A",
                '实时性': real_time,
                'CNN性能': cnn_level,
                '推荐场景': scenario
            })
        
        return pd.DataFrame(comparison_data)
    
    def create_clean_visualization(self):
        """创建简洁的可视化图表"""
        df = pd.DataFrame(self.ablation_data)
        
        # 创建2x2子图
        fig, axes = plt.subplots(2, 2, figsize=(14, 10))
        
        # 1. FPS性能对比
        configs = [row['模型配置'] for row in self.ablation_data]
        fps_values = [row['FPS'] for row in self.ablation_data]
        
        # 创建颜色映射
        colors = ['gray' if fps == 0 else 'lightblue' if fps > 12 else 'orange' if fps > 10 else 'red' 
                 for fps in fps_values]
        
        bars1 = axes[0, 0].bar(range(len(configs)), fps_values, alpha=0.8, color=colors)
        axes[0, 0].axhline(y=25, color='r', linestyle='--', label='实时阈值 (25 FPS)')
        axes[0, 0].set_xlabel('系统配置')
        axes[0, 0].set_ylabel('FPS')
        axes[0, 0].set_title('系统FPS性能对比')
        axes[0, 0].set_xticks(range(len(configs)))
        axes[0, 0].set_xticklabels([f'{i+1}' for i in range(len(configs))])
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3, axis='y')
        
        # 添加数值标签
        for i, (bar, fps) in enumerate(zip(bars1, fps_values)):
            if fps > 0:
                axes[0, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.3,
                               f'{fps:.1f}', ha='center', va='bottom', fontweight='bold')
        
        # 2. CNN预测R²对比
        cnn_configs = df[df['CNN预测R²'] > 0]
        if len(cnn_configs) > 0:
            cnn_indices = cnn_configs.index.tolist()
            cnn_r2_values = cnn_configs['CNN预测R²'].tolist()
            
            bars2 = axes[0, 1].bar(range(len(cnn_indices)), cnn_r2_values, 
                                  alpha=0.8, color='lightgreen')
            axes[0, 1].set_xlabel('CNN配置')
            axes[0, 1].set_ylabel('R² Score')
            axes[0, 1].set_title('CNN预测R²性能')
            axes[0, 1].set_xticks(range(len(cnn_indices)))
            axes[0, 1].set_xticklabels([f'{i+1}' for i in cnn_indices])
            axes[0, 1].grid(True, alpha=0.3, axis='y')
            axes[0, 1].set_ylim(0.9, 0.92)
            
            # 添加数值标签
            for bar, r2 in zip(bars2, cnn_r2_values):
                axes[0, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.0005,
                               f'{r2:.4f}', ha='center', va='bottom', fontweight='bold')
        
        # 3. CNN误差对比
        if len(cnn_configs) > 0:
            rmse_values = cnn_configs['RMSE'].tolist()
            mae_values = cnn_configs['MAE'].tolist()
            
            x = np.arange(len(cnn_indices))
            width = 0.35
            
            bars3 = axes[1, 0].bar(x - width/2, rmse_values, width, 
                                  label='RMSE', alpha=0.8, color='lightcoral')
            bars4 = axes[1, 0].bar(x + width/2, mae_values, width, 
                                  label='MAE', alpha=0.8, color='lightsalmon')
            
            axes[1, 0].set_xlabel('CNN配置')
            axes[1, 0].set_ylabel('误差值')
            axes[1, 0].set_title('CNN预测误差对比')
            axes[1, 0].set_xticks(x)
            axes[1, 0].set_xticklabels([f'{i+1}' for i in cnn_indices])
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3, axis='y')
        
        # 4. 性能权衡散点图
        if len(cnn_configs) > 0:
            cnn_fps = cnn_configs['FPS'].tolist()
            cnn_r2 = cnn_configs['CNN预测R²'].tolist()
            
            scatter = axes[1, 1].scatter(cnn_fps, cnn_r2, s=100, alpha=0.7, 
                                       c=range(len(cnn_fps)), cmap='viridis')
            axes[1, 1].set_xlabel('FPS')
            axes[1, 1].set_ylabel('CNN预测R²')
            axes[1, 1].set_title('性能权衡分析')
            axes[1, 1].grid(True, alpha=0.3)
            
            # 添加标签
            for i, (fps, r2, idx) in enumerate(zip(cnn_fps, cnn_r2, cnn_indices)):
                axes[1, 1].annotate(f'配置{idx+1}', (fps, r2), xytext=(5, 5), 
                                   textcoords='offset points', fontsize=10)
        
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        plot_filename = f"简洁消融试验图表_{timestamp}.png"
        plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"📊 可视化图表已保存: {plot_filename}")
        return plot_filename
    
    def save_clean_tables(self):
        """保存简洁的表格"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 1. 保存主要表格
        main_table = self.generate_main_table()
        main_csv = f"消融试验主表_{timestamp}.csv"
        main_table.to_csv(main_csv, index=False, encoding='utf-8-sig')
        
        # 2. 保存CNN专项表格
        cnn_table = self.generate_cnn_only_table()
        if not cnn_table.empty:
            cnn_csv = f"CNN性能表_{timestamp}.csv"
            cnn_table.to_csv(cnn_csv, index=False, encoding='utf-8-sig')
        
        # 3. 保存性能对比表格
        comparison_table = self.generate_performance_comparison()
        comparison_csv = f"性能对比表_{timestamp}.csv"
        comparison_table.to_csv(comparison_csv, index=False, encoding='utf-8-sig')
        
        # 4. 保存Excel文件
        excel_file = f"消融试验表格_{timestamp}.xlsx"
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            main_table.to_excel(writer, sheet_name='主要结果', index=False)
            if not cnn_table.empty:
                cnn_table.to_excel(writer, sheet_name='CNN性能', index=False)
            comparison_table.to_excel(writer, sheet_name='性能对比', index=False)
        
        print(f"💾 所有表格已保存:")
        print(f"  📊 主要表格: {main_csv}")
        if not cnn_table.empty:
            print(f"  🤖 CNN表格: {cnn_csv}")
        print(f"  📈 对比表格: {comparison_csv}")
        print(f"  📗 Excel文件: {excel_file}")
        
        return {
            'main_csv': main_csv,
            'cnn_csv': cnn_csv if not cnn_table.empty else None,
            'comparison_csv': comparison_csv,
            'excel_file': excel_file
        }
    
    def print_clean_summary(self):
        """打印简洁的摘要"""
        print("\n" + "=" * 70)
        print("📊 消融试验结果总结")
        print("=" * 70)
        
        # 主要表格
        print("\n🏆 主要结果:")
        main_table = self.generate_main_table()
        print(main_table.to_string(index=False))
        
        # CNN专项表格
        cnn_table = self.generate_cnn_only_table()
        if not cnn_table.empty:
            print("\n🤖 CNN性能专项:")
            print(cnn_table.to_string(index=False))
        
        # 关键指标
        print("\n💡 关键指标:")
        print("  📈 CNN预测R²: 0.9175 (优秀)")
        print("  📉 RMSE: 0.0236 (很低)")
        print("  📉 MAE: 0.0178 (很低)")
        print("  ⚡ 最高FPS: 13.7 (YOLOv11分割)")
        print("  ⚡ 完整系统FPS: 9.1")
        print("  🎯 实时性: 需要优化到25+ FPS")


def main():
    """主函数"""
    print("📊 简洁消融试验表格生成器")
    print("🎯 只保留模型配置和核心性能指标")
    print("=" * 50)
    
    try:
        # 创建表格生成器
        generator = CleanAblationTable()
        
        # 打印摘要
        generator.print_clean_summary()
        
        # 保存所有表格
        files = generator.save_clean_tables()
        
        # 创建可视化
        plot_file = generator.create_clean_visualization()
        
        print(f"\n✅ 简洁消融试验表格已生成!")
        print(f"🎉 表格简洁明了，适合论文使用!")
        
    except Exception as e:
        print(f"❌ 表格生成失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
