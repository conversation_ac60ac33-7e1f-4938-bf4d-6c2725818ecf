import torch
import cv2
import os
import numpy as np
import time
import json
import pandas as pd
from datetime import datetime
from collections import defaultdict
from ultralytics import YOLO

# 设置环境变量避免OpenMP冲突
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'


class SimplifiedSystemEvaluator:
    """
    简化的系统性能评估器
    专注于核心性能指标评估
    """
    
    def __init__(self, config):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.config = config
        
        # 性能指标存储
        self.performance_data = {
            'detection_metrics': [],
            'segmentation_metrics': [],
            'cnn_metrics': [],
            'timing_metrics': []
        }
        
        # 初始化模块
        self.init_models()
        
        print(f"🔬 Simplified System Evaluator initialized")
        print(f"🔧 Device: {self.device}")
    
    def init_models(self):
        """初始化模型"""
        # YOLO模型
        try:
            self.yolo_model = YOLO(self.config["weights"]).to(self.device)
            self.class_names = self.yolo_model.names
            print(f"✅ YOLO model loaded")
        except Exception as e:
            print(f"❌ Failed to load YOLO: {e}")
            self.yolo_model = None
        
        # CNN回归器
        try:
            from v20_enhanced_cnn_airflow_system import EnhancedCNNRegressor, EnhancedFeatureExtractor
            
            model_path = "models/improved_cnn_regressor.pth"
            if os.path.exists(model_path):
                checkpoint = torch.load(model_path, map_location=self.device, weights_only=False)
                self.cnn_regressor = EnhancedCNNRegressor(64).to(self.device)
                self.cnn_regressor.load_state_dict(checkpoint['model_state_dict'])
                self.cnn_regressor.eval()
                
                self.feature_extractor = EnhancedFeatureExtractor()
                print(f"✅ CNN regressor loaded")
            else:
                print(f"❌ CNN model not found")
                self.cnn_regressor = None
                self.feature_extractor = None
        except Exception as e:
            print(f"❌ Failed to load CNN: {e}")
            self.cnn_regressor = None
            self.feature_extractor = None
    
    def evaluate_frame(self, frame, frame_id):
        """评估单帧性能"""
        frame_start_time = time.time()
        
        # 1. YOLO检测和分割
        detection_start = time.time()
        
        results = self.yolo_model.predict(
            source=frame,
            imgsz=640,
            conf=0.5,
            iou=0.5,
            device=self.device,
            verbose=False
        )
        
        detection_time = time.time() - detection_start
        
        # 提取检测结果
        detections = []
        masks = []
        
        for result in results:
            if result.boxes is not None:
                boxes = result.boxes.xywh.cpu().numpy()
                confs = result.boxes.conf.cpu().numpy()
                clss = result.boxes.cls.cpu().numpy().astype(int)
                
                for box, conf, cls_id in zip(boxes, confs, clss):
                    detections.append({
                        'bbox': box,
                        'confidence': conf,
                        'class_id': cls_id,
                        'class_name': self.class_names[cls_id]
                    })
            
            if result.masks is not None:
                masks_data = result.masks.data.cpu().numpy()
                masks.extend(masks_data)
        
        # 2. 分割质量评估
        segmentation_metrics = self.evaluate_segmentation(masks, frame.shape[:2])
        
        # 3. CNN预测评估
        cnn_metrics = []
        if masks and len(masks) >= 1 and self.cnn_regressor:
            # 使用第一个掩膜作为草莓，第二个作为遮挡（如果存在）
            strawberry_mask = (masks[0] * 255).astype(np.uint8)
            obstacle_mask = (masks[1] * 255).astype(np.uint8) if len(masks) > 1 else np.zeros_like(strawberry_mask)
            
            # 调整掩膜尺寸
            h, w = frame.shape[:2]
            if strawberry_mask.shape != (h, w):
                strawberry_mask = cv2.resize(strawberry_mask, (w, h))
            if obstacle_mask.shape != (h, w):
                obstacle_mask = cv2.resize(obstacle_mask, (w, h))
            
            cnn_result = self.evaluate_cnn_prediction(frame, strawberry_mask, obstacle_mask)
            cnn_metrics.append(cnn_result)
        
        # 计算总帧时间
        frame_total_time = time.time() - frame_start_time
        
        # 组织帧指标
        frame_metrics = {
            'frame_id': frame_id,
            'total_time': frame_total_time,
            'fps': 1.0 / frame_total_time if frame_total_time > 0 else 0,
            'detection': {
                'time': detection_time,
                'fps': 1.0 / detection_time if detection_time > 0 else 0,
                'count': len(detections),
                'strawberry_count': len([d for d in detections if d['class_id'] == 0]),
                'obstacle_count': len([d for d in detections if d['class_id'] == 2]),
                'avg_confidence': np.mean([d['confidence'] for d in detections]) if detections else 0
            },
            'segmentation': segmentation_metrics,
            'cnn': cnn_metrics
        }
        
        return frame_metrics
    
    def evaluate_segmentation(self, masks, frame_shape):
        """评估分割质量"""
        if not masks:
            return {
                'mask_count': 0,
                'total_area': 0,
                'avg_area': 0,
                'coverage_ratio': 0
            }
        
        total_pixels = frame_shape[0] * frame_shape[1]
        mask_areas = []
        total_mask_area = 0
        
        for mask in masks:
            # 调整掩膜尺寸
            if mask.shape != frame_shape:
                mask = cv2.resize(mask, (frame_shape[1], frame_shape[0]))
            
            area = np.sum(mask > 0.5)
            mask_areas.append(area)
            total_mask_area += area
        
        return {
            'mask_count': len(masks),
            'total_area': total_mask_area,
            'avg_area': np.mean(mask_areas) if mask_areas else 0,
            'coverage_ratio': total_mask_area / total_pixels,
            'area_std': np.std(mask_areas) if mask_areas else 0
        }
    
    def evaluate_cnn_prediction(self, frame, strawberry_mask, obstacle_mask):
        """评估CNN预测性能"""
        if not self.cnn_regressor or not self.feature_extractor:
            return {
                'time': 0,
                'fps': 0,
                'prediction': 0,
                'success': False
            }
        
        start_time = time.time()
        
        try:
            # 特征提取
            features = self.feature_extractor.extract_simple_features(
                frame, strawberry_mask, obstacle_mask
            )
            
            if features is None:
                return {'time': 0, 'fps': 0, 'prediction': 0, 'success': False}
            
            # CNN预测
            features_tensor = torch.FloatTensor(features).unsqueeze(0).to(self.device)
            
            with torch.no_grad():
                prediction = self.cnn_regressor(features_tensor).item()
            
            prediction_time = time.time() - start_time
            
            return {
                'time': prediction_time,
                'fps': 1.0 / prediction_time if prediction_time > 0 else 0,
                'prediction': prediction,
                'success': True
            }
            
        except Exception as e:
            print(f"⚠️ CNN prediction failed: {e}")
            return {'time': 0, 'fps': 0, 'prediction': 0, 'success': False}
    
    def evaluate_video(self, video_path, max_frames=100):
        """评估视频性能"""
        print(f"🎬 Evaluating: {os.path.basename(video_path)}")
        
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print(f"❌ Cannot open video: {video_path}")
            return None
        
        # 视频信息
        width, height = int(cap.get(3)), int(cap.get(4))
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        if max_frames:
            total_frames = min(total_frames, max_frames)
        
        print(f"📹 Video: {width}x{height}, {fps}fps, processing {total_frames} frames")
        
        # 评估每一帧
        frame_results = []
        system_start_time = time.time()
        
        frame_count = 0
        
        while cap.isOpened() and frame_count < total_frames:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # 评估当前帧
            frame_metrics = self.evaluate_frame(frame, frame_count)
            frame_results.append(frame_metrics)
            
            # 进度显示
            if frame_count % 25 == 0:
                progress = (frame_count / total_frames) * 100
                avg_fps = frame_count / (time.time() - system_start_time)
                print(f"  📊 Progress: {frame_count}/{total_frames} ({progress:.1f}%), Avg FPS: {avg_fps:.1f}")
        
        cap.release()
        
        total_time = time.time() - system_start_time
        
        # 计算整体指标
        overall_metrics = self.calculate_overall_metrics(frame_results, total_time)
        
        # 组织结果
        evaluation_result = {
            'video_info': {
                'path': video_path,
                'width': width,
                'height': height,
                'original_fps': fps,
                'total_frames': total_frames,
                'processed_frames': frame_count
            },
            'overall_metrics': overall_metrics,
            'frame_results': frame_results
        }
        
        return evaluation_result
    
    def calculate_overall_metrics(self, frame_results, total_time):
        """计算整体性能指标"""
        # 提取时间数据
        frame_times = [f['total_time'] for f in frame_results]
        detection_times = [f['detection']['time'] for f in frame_results]
        
        # CNN数据
        cnn_times = []
        cnn_predictions = []
        successful_cnns = 0
        
        for f in frame_results:
            for cnn in f['cnn']:
                if cnn['success']:
                    cnn_times.append(cnn['time'])
                    cnn_predictions.append(cnn['prediction'])
                    successful_cnns += 1
        
        # 检测数据
        total_detections = sum(f['detection']['count'] for f in frame_results)
        total_strawberries = sum(f['detection']['strawberry_count'] for f in frame_results)
        
        # 分割数据
        total_masks = sum(f['segmentation']['mask_count'] for f in frame_results)
        
        return {
            'system_performance': {
                'total_time': total_time,
                'total_frames': len(frame_results),
                'avg_fps': len(frame_results) / total_time if total_time > 0 else 0,
                'real_time_capable': (len(frame_results) / total_time) >= 25 if total_time > 0 else False
            },
            'detection_performance': {
                'avg_time': np.mean(detection_times),
                'avg_fps': 1.0 / np.mean(detection_times) if detection_times else 0,
                'total_detections': total_detections,
                'total_strawberries': total_strawberries,
                'avg_per_frame': total_detections / len(frame_results) if frame_results else 0
            },
            'segmentation_performance': {
                'total_masks': total_masks,
                'avg_per_frame': total_masks / len(frame_results) if frame_results else 0
            },
            'cnn_performance': {
                'avg_time': np.mean(cnn_times) if cnn_times else 0,
                'avg_fps': 1.0 / np.mean(cnn_times) if cnn_times else 0,
                'successful_predictions': successful_cnns,
                'success_rate': successful_cnns / len(frame_results) if frame_results else 0,
                'avg_prediction': np.mean(cnn_predictions) if cnn_predictions else 0
            },
            'efficiency_analysis': {
                'detection_ratio': np.mean(detection_times) / np.mean(frame_times) if frame_times else 0,
                'cnn_ratio': np.mean(cnn_times) / np.mean(frame_times) if cnn_times and frame_times else 0
            }
        }
    
    def save_results(self, results):
        """保存评估结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存JSON结果
        json_file = f"system_evaluation_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        # 创建CSV摘要
        metrics = results['overall_metrics']
        summary_data = {
            'Video': [os.path.basename(results['video_info']['path'])],
            'Total_Frames': [results['video_info']['processed_frames']],
            'Processing_Time_s': [metrics['system_performance']['total_time']],
            'Average_FPS': [metrics['system_performance']['avg_fps']],
            'Real_Time': [metrics['system_performance']['real_time_capable']],
            'Detection_FPS': [metrics['detection_performance']['avg_fps']],
            'CNN_FPS': [metrics['cnn_performance']['avg_fps']],
            'Total_Strawberries': [metrics['detection_performance']['total_strawberries']],
            'CNN_Success_Rate': [metrics['cnn_performance']['success_rate']],
            'Detection_Time_Ratio': [metrics['efficiency_analysis']['detection_ratio']],
            'CNN_Time_Ratio': [metrics['efficiency_analysis']['cnn_ratio']]
        }
        
        df = pd.DataFrame(summary_data)
        csv_file = f"system_summary_{timestamp}.csv"
        df.to_csv(csv_file, index=False, encoding='utf-8-sig')
        
        print(f"💾 Results saved:")
        print(f"  📄 JSON: {json_file}")
        print(f"  📊 CSV: {csv_file}")
        
        return json_file, csv_file
    
    def print_summary(self, results):
        """打印性能摘要"""
        metrics = results['overall_metrics']
        video_info = results['video_info']
        
        print("\n" + "=" * 70)
        print("🎯 SYSTEM PERFORMANCE EVALUATION SUMMARY")
        print("=" * 70)
        
        print(f"📹 Video: {os.path.basename(video_info['path'])}")
        print(f"📊 Frames: {video_info['processed_frames']}")
        print(f"⏱️ Total Time: {metrics['system_performance']['total_time']:.2f}s")
        print(f"🚀 Average FPS: {metrics['system_performance']['avg_fps']:.1f}")
        print(f"⚡ Real-time: {'✅ Yes' if metrics['system_performance']['real_time_capable'] else '❌ No'}")
        
        print(f"\n🔍 Detection Performance:")
        det = metrics['detection_performance']
        print(f"  FPS: {det['avg_fps']:.1f}")
        print(f"  Total Strawberries: {det['total_strawberries']}")
        print(f"  Avg per Frame: {det['avg_per_frame']:.1f}")
        
        print(f"\n🤖 CNN Performance:")
        cnn = metrics['cnn_performance']
        print(f"  FPS: {cnn['avg_fps']:.1f}")
        print(f"  Success Rate: {cnn['success_rate']:.1%}")
        print(f"  Avg Prediction: {cnn['avg_prediction']:.3f}")
        
        print(f"\n⚙️ Efficiency Analysis:")
        eff = metrics['efficiency_analysis']
        print(f"  Detection Time: {eff['detection_ratio']:.1%}")
        print(f"  CNN Time: {eff['cnn_ratio']:.1%}")
        
        # 性能评级
        avg_fps = metrics['system_performance']['avg_fps']
        if avg_fps >= 30:
            rating = "🌟 Excellent"
        elif avg_fps >= 25:
            rating = "✅ Good (Real-time)"
        elif avg_fps >= 15:
            rating = "🟡 Fair"
        else:
            rating = "🔴 Poor"
        
        print(f"\n🏆 Overall Rating: {rating}")


def main():
    """主函数"""
    print("🔬 Simplified System Performance Evaluation")
    print("🎯 Evaluating YOLOv11-seg + CNN Regressor Performance")
    print("=" * 60)
    
    config = {
        "weights": "weights/best.pt",
        "input": "视频/1.mp4"
    }
    
    try:
        # 创建评估器
        evaluator = SimplifiedSystemEvaluator(config)
        
        # 评估视频
        results = evaluator.evaluate_video(config["input"], max_frames=100)
        
        if results:
            # 打印摘要
            evaluator.print_summary(results)
            
            # 保存结果
            evaluator.save_results(results)
            
            print(f"\n✅ System evaluation completed successfully!")
        else:
            print(f"❌ Evaluation failed!")
        
    except Exception as e:
        print(f"❌ Evaluation error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
