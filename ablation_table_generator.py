import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import json
import os

# 设置matplotlib支持中文
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class AblationTableGenerator:
    """消融试验表格生成器"""
    
    def __init__(self):
        # 消融试验数据 (基于之前的试验结果)
        self.ablation_data = [
            {
                'Model': 'Baseline FC',
                'Architecture': 'FC',
                'Dropout': '✗',
                'Input_BatchNorm': '✗',
                'Conv_BatchNorm': '✗',
                'FC_BatchNorm': '✗',
                'R2_Score': 0.9459,
                'RMSE': 0.0189,
                'MAE': 0.0170,
                'MAPE': 15.2,
                'Training_Epochs': 107,
                'Parameters': '2.1K',
                'Inference_Time_ms': 0.8
            },
            {
                'Model': 'Baseline FC + Dropout',
                'Architecture': 'FC',
                'Dropout': '✓',
                'Input_BatchNorm': '✗',
                'Conv_BatchNorm': '✗',
                'FC_BatchNorm': '✗',
                'R2_Score': 0.9249,
                'RMSE': 0.0223,
                'MAE': 0.0185,
                'MAPE': 18.7,
                'Training_Epochs': 130,
                'Parameters': '2.1K',
                'Inference_Time_ms': 0.9
            },
            {
                'Model': 'CNN (No Norm)',
                'Architecture': 'CNN',
                'Dropout': '✓',
                'Input_BatchNorm': '✗',
                'Conv_BatchNorm': '✗',
                'FC_BatchNorm': '✗',
                'R2_Score': 0.5824,
                'RMSE': 0.0526,
                'MAE': 0.0441,
                'MAPE': 45.2,
                'Training_Epochs': 65,
                'Parameters': '15.3K',
                'Inference_Time_ms': 2.1
            },
            {
                'Model': 'CNN + Input Norm',
                'Architecture': 'CNN',
                'Dropout': '✓',
                'Input_BatchNorm': '✓',
                'Conv_BatchNorm': '✗',
                'FC_BatchNorm': '✗',
                'R2_Score': 0.8497,
                'RMSE': 0.0316,
                'MAE': 0.0243,
                'MAPE': 27.5,
                'Training_Epochs': 200,
                'Parameters': '15.4K',
                'Inference_Time_ms': 2.3
            },
            {
                'Model': 'CNN + Input + Conv Norm',
                'Architecture': 'CNN',
                'Dropout': '✓',
                'Input_BatchNorm': '✓',
                'Conv_BatchNorm': '✓',
                'FC_BatchNorm': '✗',
                'R2_Score': 0.9225,
                'RMSE': 0.0227,
                'MAE': 0.0173,
                'MAPE': 19.2,
                'Training_Epochs': 181,
                'Parameters': '15.6K',
                'Inference_Time_ms': 2.5
            },
            {
                'Model': 'Full Model (All Modules)',
                'Architecture': 'CNN',
                'Dropout': '✓',
                'Input_BatchNorm': '✓',
                'Conv_BatchNorm': '✓',
                'FC_BatchNorm': '✓',
                'R2_Score': 0.9057,
                'RMSE': 0.0250,
                'MAE': 0.0198,
                'MAPE': 22.1,
                'Training_Epochs': 132,
                'Parameters': '15.8K',
                'Inference_Time_ms': 2.7
            }
        ]
        
        print("📊 Ablation Table Generator initialized")
    
    def generate_main_table(self):
        """生成主要的消融试验表格"""
        df = pd.DataFrame(self.ablation_data)
        
        # 计算相对于基线的改进
        baseline_r2 = df.iloc[0]['R2_Score']
        df['R2_Improvement'] = ((df['R2_Score'] - baseline_r2) / baseline_r2 * 100).round(1)
        
        # 重新排列列顺序
        main_columns = [
            'Model', 'Architecture', 'Dropout', 'Input_BatchNorm', 
            'Conv_BatchNorm', 'FC_BatchNorm', 'R2_Score', 'RMSE', 
            'MAE', 'MAPE', 'R2_Improvement'
        ]
        
        main_table = df[main_columns].copy()
        
        # 格式化数值
        main_table['R2_Score'] = main_table['R2_Score'].apply(lambda x: f"{x:.4f}")
        main_table['RMSE'] = main_table['RMSE'].apply(lambda x: f"{x:.4f}")
        main_table['MAE'] = main_table['MAE'].apply(lambda x: f"{x:.4f}")
        main_table['MAPE'] = main_table['MAPE'].apply(lambda x: f"{x:.1f}%")
        main_table['R2_Improvement'] = main_table['R2_Improvement'].apply(
            lambda x: f"{x:+.1f}%" if x != 0 else "0.0%"
        )
        
        return main_table
    
    def generate_detailed_table(self):
        """生成详细的性能表格"""
        df = pd.DataFrame(self.ablation_data)
        
        # 详细表格包含所有指标
        detailed_columns = [
            'Model', 'R2_Score', 'RMSE', 'MAE', 'MAPE', 
            'Training_Epochs', 'Parameters', 'Inference_Time_ms'
        ]
        
        detailed_table = df[detailed_columns].copy()
        
        # 格式化数值
        detailed_table['R2_Score'] = detailed_table['R2_Score'].apply(lambda x: f"{x:.4f}")
        detailed_table['RMSE'] = detailed_table['RMSE'].apply(lambda x: f"{x:.4f}")
        detailed_table['MAE'] = detailed_table['MAE'].apply(lambda x: f"{x:.4f}")
        detailed_table['MAPE'] = detailed_table['MAPE'].apply(lambda x: f"{x:.1f}%")
        detailed_table['Inference_Time_ms'] = detailed_table['Inference_Time_ms'].apply(lambda x: f"{x:.1f}")
        
        return detailed_table
    
    def generate_module_contribution_table(self):
        """生成模块贡献分析表格"""
        df = pd.DataFrame(self.ablation_data)
        
        # 计算各模块的贡献
        baseline_r2 = df.iloc[0]['R2_Score']  # Baseline FC
        dropout_r2 = df.iloc[1]['R2_Score']   # Baseline FC + Dropout
        cnn_r2 = df.iloc[2]['R2_Score']       # CNN (No Norm)
        input_norm_r2 = df.iloc[3]['R2_Score'] # CNN + Input Norm
        conv_norm_r2 = df.iloc[4]['R2_Score']  # CNN + Input + Conv Norm
        full_r2 = df.iloc[5]['R2_Score']       # Full Model
        
        contributions = [
            {
                'Module': 'Dropout (on FC)',
                'Contribution': dropout_r2 - baseline_r2,
                'Relative_Change': ((dropout_r2 - baseline_r2) / baseline_r2) * 100,
                'Description': 'Adding dropout to baseline FC network'
            },
            {
                'Module': 'CNN Architecture',
                'Contribution': cnn_r2 - baseline_r2,
                'Relative_Change': ((cnn_r2 - baseline_r2) / baseline_r2) * 100,
                'Description': 'Replacing FC with CNN architecture'
            },
            {
                'Module': 'Input BatchNorm',
                'Contribution': input_norm_r2 - cnn_r2,
                'Relative_Change': ((input_norm_r2 - cnn_r2) / cnn_r2) * 100,
                'Description': 'Adding input feature normalization'
            },
            {
                'Module': 'Conv BatchNorm',
                'Contribution': conv_norm_r2 - input_norm_r2,
                'Relative_Change': ((conv_norm_r2 - input_norm_r2) / input_norm_r2) * 100,
                'Description': 'Adding convolutional layer normalization'
            },
            {
                'Module': 'FC BatchNorm',
                'Contribution': full_r2 - conv_norm_r2,
                'Relative_Change': ((full_r2 - conv_norm_r2) / conv_norm_r2) * 100,
                'Description': 'Adding fully connected layer normalization'
            }
        ]
        
        contrib_df = pd.DataFrame(contributions)
        
        # 格式化数值
        contrib_df['Contribution'] = contrib_df['Contribution'].apply(lambda x: f"{x:+.4f}")
        contrib_df['Relative_Change'] = contrib_df['Relative_Change'].apply(lambda x: f"{x:+.1f}%")
        
        return contrib_df
    
    def generate_latex_table(self, table_type='main'):
        """生成LaTeX格式的表格"""
        if table_type == 'main':
            df = self.generate_main_table()
            caption = "Ablation Study Results: Impact of Different Modules on Model Performance"
            label = "tab:ablation_main"
        elif table_type == 'detailed':
            df = self.generate_detailed_table()
            caption = "Detailed Performance Metrics for Ablation Study"
            label = "tab:ablation_detailed"
        else:
            df = self.generate_module_contribution_table()
            caption = "Module Contribution Analysis"
            label = "tab:ablation_contribution"
        
        # 生成LaTeX代码
        latex_code = f"""
\\begin{{table}}[htbp]
\\centering
\\caption{{{caption}}}
\\label{{{label}}}
\\begin{{tabular}}{{{'|'.join(['c'] * len(df.columns))}}}
\\hline
"""
        
        # 添加表头
        headers = ' & '.join([col.replace('_', '\\_') for col in df.columns])
        latex_code += f"{headers} \\\\\n\\hline\n"
        
        # 添加数据行
        for _, row in df.iterrows():
            row_data = ' & '.join([str(val).replace('_', '\\_') for val in row.values])
            latex_code += f"{row_data} \\\\\n"
        
        latex_code += """\\hline
\\end{tabular}
\\end{table}
"""
        
        return latex_code
    
    def create_visualization(self):
        """创建可视化图表"""
        df = pd.DataFrame(self.ablation_data)
        
        # 创建多子图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. R²分数对比
        models = [row['Model'] for row in self.ablation_data]
        r2_scores = [row['R2_Score'] for row in self.ablation_data]
        
        bars1 = axes[0, 0].bar(range(len(models)), r2_scores, alpha=0.8, color='skyblue')
        axes[0, 0].set_xlabel('Model Configuration')
        axes[0, 0].set_ylabel('R² Score')
        axes[0, 0].set_title('R² Score Comparison Across Configurations')
        axes[0, 0].set_xticks(range(len(models)))
        axes[0, 0].set_xticklabels([f'M{i+1}' for i in range(len(models))], rotation=45)
        axes[0, 0].grid(True, alpha=0.3, axis='y')
        
        # 添加数值标签
        for bar, score in zip(bars1, r2_scores):
            axes[0, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005,
                           f'{score:.3f}', ha='center', va='bottom', fontweight='bold')
        
        # 2. 误差指标对比
        rmse_values = [row['RMSE'] for row in self.ablation_data]
        mae_values = [row['MAE'] for row in self.ablation_data]
        
        x = np.arange(len(models))
        width = 0.35
        
        bars2 = axes[0, 1].bar(x - width/2, rmse_values, width, label='RMSE', alpha=0.8, color='lightcoral')
        bars3 = axes[0, 1].bar(x + width/2, mae_values, width, label='MAE', alpha=0.8, color='lightgreen')
        
        axes[0, 1].set_xlabel('Model Configuration')
        axes[0, 1].set_ylabel('Error Value')
        axes[0, 1].set_title('Error Metrics Comparison')
        axes[0, 1].set_xticks(x)
        axes[0, 1].set_xticklabels([f'M{i+1}' for i in range(len(models))], rotation=45)
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3, axis='y')
        
        # 3. 训练效率分析
        epochs = [row['Training_Epochs'] for row in self.ablation_data]
        inference_times = [row['Inference_Time_ms'] for row in self.ablation_data]
        
        # 双y轴图
        ax3 = axes[1, 0]
        ax3_twin = ax3.twinx()
        
        line1 = ax3.plot(range(len(models)), epochs, 'bo-', label='Training Epochs', linewidth=2, markersize=8)
        line2 = ax3_twin.plot(range(len(models)), inference_times, 'ro-', label='Inference Time (ms)', linewidth=2, markersize=8)
        
        ax3.set_xlabel('Model Configuration')
        ax3.set_ylabel('Training Epochs', color='blue')
        ax3_twin.set_ylabel('Inference Time (ms)', color='red')
        ax3.set_title('Training Efficiency vs Inference Speed')
        ax3.set_xticks(range(len(models)))
        ax3.set_xticklabels([f'M{i+1}' for i in range(len(models))], rotation=45)
        ax3.grid(True, alpha=0.3)
        
        # 合并图例
        lines1, labels1 = ax3.get_legend_handles_labels()
        lines2, labels2 = ax3_twin.get_legend_handles_labels()
        ax3.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
        
        # 4. 模块贡献热力图
        contrib_df = self.generate_module_contribution_table()
        contrib_values = [float(val.replace('+', '').replace('%', '')) for val in contrib_df['Relative_Change']]
        module_names = contrib_df['Module'].tolist()
        
        # 创建热力图数据
        heatmap_data = np.array(contrib_values).reshape(1, -1)
        
        im = axes[1, 1].imshow(heatmap_data, cmap='RdYlGn', aspect='auto')
        axes[1, 1].set_xticks(range(len(module_names)))
        axes[1, 1].set_xticklabels(module_names, rotation=45, ha='right')
        axes[1, 1].set_yticks([0])
        axes[1, 1].set_yticklabels(['Contribution'])
        axes[1, 1].set_title('Module Contribution Heatmap')
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=axes[1, 1])
        cbar.set_label('Relative Change (%)')
        
        # 添加数值标签
        for i, val in enumerate(contrib_values):
            axes[1, 1].text(i, 0, f'{val:+.1f}%', ha='center', va='center', 
                           color='white' if abs(val) > 20 else 'black', fontweight='bold')
        
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        plot_filename = f"ablation_study_visualization_{timestamp}.png"
        plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"📊 Visualization saved: {plot_filename}")
        
        return plot_filename
    
    def save_all_tables(self):
        """保存所有表格"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 1. 保存主要表格
        main_table = self.generate_main_table()
        main_csv = f"ablation_main_table_{timestamp}.csv"
        main_table.to_csv(main_csv, index=False, encoding='utf-8-sig')
        
        # 2. 保存详细表格
        detailed_table = self.generate_detailed_table()
        detailed_csv = f"ablation_detailed_table_{timestamp}.csv"
        detailed_table.to_csv(detailed_csv, index=False, encoding='utf-8-sig')
        
        # 3. 保存模块贡献表格
        contrib_table = self.generate_module_contribution_table()
        contrib_csv = f"ablation_contribution_table_{timestamp}.csv"
        contrib_table.to_csv(contrib_csv, index=False, encoding='utf-8-sig')
        
        # 4. 保存LaTeX代码
        latex_file = f"ablation_latex_tables_{timestamp}.tex"
        with open(latex_file, 'w', encoding='utf-8') as f:
            f.write("% Main Ablation Table\n")
            f.write(self.generate_latex_table('main'))
            f.write("\n\n% Detailed Performance Table\n")
            f.write(self.generate_latex_table('detailed'))
            f.write("\n\n% Module Contribution Table\n")
            f.write(self.generate_latex_table('contribution'))
        
        # 5. 保存Excel文件（多个工作表）
        excel_file = f"ablation_study_tables_{timestamp}.xlsx"
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            main_table.to_excel(writer, sheet_name='Main_Results', index=False)
            detailed_table.to_excel(writer, sheet_name='Detailed_Metrics', index=False)
            contrib_table.to_excel(writer, sheet_name='Module_Contribution', index=False)
        
        print(f"💾 All tables saved:")
        print(f"  📊 Main table: {main_csv}")
        print(f"  📋 Detailed table: {detailed_csv}")
        print(f"  🔍 Contribution table: {contrib_csv}")
        print(f"  📄 LaTeX code: {latex_file}")
        print(f"  📗 Excel file: {excel_file}")
        
        return {
            'main_csv': main_csv,
            'detailed_csv': detailed_csv,
            'contribution_csv': contrib_csv,
            'latex_file': latex_file,
            'excel_file': excel_file
        }
    
    def print_summary(self):
        """打印消融试验摘要"""
        print("\n" + "=" * 80)
        print("📊 ABLATION STUDY TABLE SUMMARY")
        print("=" * 80)
        
        # 主要表格
        print("\n🏆 Main Results Table:")
        main_table = self.generate_main_table()
        print(main_table.to_string(index=False))
        
        # 模块贡献
        print("\n🔍 Module Contribution Analysis:")
        contrib_table = self.generate_module_contribution_table()
        print(contrib_table[['Module', 'Contribution', 'Relative_Change']].to_string(index=False))
        
        # 关键发现
        print("\n💡 Key Findings:")
        print("  1. 简单FC网络表现最佳 (R² = 0.9459)")
        print("  2. Input BatchNorm是最重要的模块 (+45.9%)")
        print("  3. CNN架构对此任务反而有害 (-38.4%)")
        print("  4. 过度正则化(Dropout)降低性能 (-2.2%)")
        print("  5. 模型复杂度与性能不成正比")


def main():
    """主函数"""
    print("📊 Ablation Study Table Generator")
    print("🎯 Generating comprehensive tables and visualizations")
    print("=" * 60)
    
    try:
        # 创建表格生成器
        generator = AblationTableGenerator()
        
        # 打印摘要
        generator.print_summary()
        
        # 保存所有表格
        files = generator.save_all_tables()
        
        # 创建可视化
        plot_file = generator.create_visualization()
        
        print(f"\n✅ All ablation study tables and visualizations generated!")
        print(f"🎉 Ready for academic paper and presentation!")
        
    except Exception as e:
        print(f"❌ Table generation failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
