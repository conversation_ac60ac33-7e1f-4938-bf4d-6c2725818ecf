{"Data_Preprocessing_Analysis": {"Raw_Data_Average_R2": 0.8974, "StandardScaled_Average_R2": 0.74115, "MinMaxScaled_Average_R2": 0.40099999999999997}, "Model_Architecture_Analysis": {"BasicCNN_Average_R2": 0.45576666666666665, "NormalizedCNN_Average_R2": 0.9039333333333334}, "Best_Configuration": {"Experiment": "原始数据 + 归一化CNN（有BatchNorm）", "R2_Score": 0.9245, "RMSE": 0.0224, "MAE": 0.017}, "Key_Findings": ["原始数据表现最好，数据预处理反而降低性能", "BatchNorm归一化是性能提升的关键因素", "原始数据+归一化CNN是最佳配置组合", "模型内部归一化比外部数据预处理更有效"]}