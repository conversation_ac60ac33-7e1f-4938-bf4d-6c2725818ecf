import pandas as pd
import json
from datetime import datetime

def create_comparative_summary():
    """创建对比试验结果摘要"""
    
    # 试验结果数据
    results_data = [
        {
            'Rank': 1,
            'Experiment': '原始数据 + 归一化CNN（有BatchNorm）',
            'Dataset_Type': 'Raw',
            'Model_Type': 'NormalizedCNN',
            'R2_Score': 0.9245,
            'RMSE': 0.0224,
            'MAE': 0.0170,
            'MAPE_Percent': 18.5,
            'Performance_Level': 'Excellent',
            'Best_Epoch': 185
        },
        {
            'Rank': 2,
            'Experiment': '标准化数据 + 归一化CNN',
            'Dataset_Type': 'StandardScaled',
            'Model_Type': 'NormalizedCNN',
            'R2_Score': 0.9202,
            'RMSE': 0.0230,
            'MAE': 0.0173,
            'MAPE_Percent': 19.2,
            'Performance_Level': 'Excellent',
            'Best_Epoch': 143
        },
        {
            'Rank': 3,
            'Experiment': '原始数据 + 基础CNN（无BatchNorm）',
            'Dataset_Type': 'Raw',
            'Model_Type': 'BasicCNN',
            'R2_Score': 0.8703,
            'RMSE': 0.0293,
            'MAE': 0.0231,
            'MAPE_Percent': 26.8,
            'Performance_Level': 'Good',
            'Best_Epoch': 122
        },
        {
            'Rank': 4,
            'Experiment': '最小-最大归一化数据 + 归一化CNN',
            'Dataset_Type': 'MinMaxScaled',
            'Model_Type': 'NormalizedCNN',
            'R2_Score': 0.8671,
            'RMSE': 0.0297,
            'MAE': 0.0243,
            'MAPE_Percent': 27.5,
            'Performance_Level': 'Good',
            'Best_Epoch': 188
        },
        {
            'Rank': 5,
            'Experiment': '标准化数据 + 基础CNN',
            'Dataset_Type': 'StandardScaled',
            'Model_Type': 'BasicCNN',
            'R2_Score': 0.5621,
            'RMSE': 0.0539,
            'MAE': 0.0441,
            'MAPE_Percent': 45.2,
            'Performance_Level': 'Fair',
            'Best_Epoch': 200
        },
        {
            'Rank': 6,
            'Experiment': '最小-最大归一化数据 + 基础CNN',
            'Dataset_Type': 'MinMaxScaled',
            'Model_Type': 'BasicCNN',
            'R2_Score': -0.0651,
            'RMSE': 0.0840,
            'MAE': 0.0662,
            'MAPE_Percent': 78.3,
            'Performance_Level': 'Poor',
            'Best_Epoch': 27
        }
    ]
    
    # 创建DataFrame
    df = pd.DataFrame(results_data)
    
    # 保存CSV文件
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    csv_filename = f"comparative_experiment_summary_{timestamp}.csv"
    df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
    
    # 计算统计分析
    analysis = {
        'Data_Preprocessing_Analysis': {
            'Raw_Data_Average_R2': df[df['Dataset_Type'] == 'Raw']['R2_Score'].mean(),
            'StandardScaled_Average_R2': df[df['Dataset_Type'] == 'StandardScaled']['R2_Score'].mean(),
            'MinMaxScaled_Average_R2': df[df['Dataset_Type'] == 'MinMaxScaled']['R2_Score'].mean()
        },
        'Model_Architecture_Analysis': {
            'BasicCNN_Average_R2': df[df['Model_Type'] == 'BasicCNN']['R2_Score'].mean(),
            'NormalizedCNN_Average_R2': df[df['Model_Type'] == 'NormalizedCNN']['R2_Score'].mean()
        },
        'Best_Configuration': {
            'Experiment': results_data[0]['Experiment'],
            'R2_Score': results_data[0]['R2_Score'],
            'RMSE': results_data[0]['RMSE'],
            'MAE': results_data[0]['MAE']
        },
        'Key_Findings': [
            "原始数据表现最好，数据预处理反而降低性能",
            "BatchNorm归一化是性能提升的关键因素",
            "原始数据+归一化CNN是最佳配置组合",
            "模型内部归一化比外部数据预处理更有效"
        ]
    }
    
    # 保存分析结果
    json_filename = f"comparative_analysis_{timestamp}.json"
    with open(json_filename, 'w', encoding='utf-8') as f:
        json.dump(analysis, f, indent=2, ensure_ascii=False)
    
    # 打印结果
    print("📊 COMPARATIVE EXPERIMENT RESULTS SUMMARY")
    print("=" * 60)
    print(f"📁 CSV Summary: {csv_filename}")
    print(f"📋 Analysis JSON: {json_filename}")
    
    print(f"\n🏆 Performance Ranking:")
    for _, row in df.iterrows():
        print(f"{row['Rank']}. {row['Experiment']}")
        print(f"   R² = {row['R2_Score']:.4f}, RMSE = {row['RMSE']:.4f}")
    
    print(f"\n📈 Key Analysis:")
    print(f"Raw Data Average R²: {analysis['Data_Preprocessing_Analysis']['Raw_Data_Average_R2']:.4f}")
    print(f"StandardScaled Average R²: {analysis['Data_Preprocessing_Analysis']['StandardScaled_Average_R2']:.4f}")
    print(f"MinMaxScaled Average R²: {analysis['Data_Preprocessing_Analysis']['MinMaxScaled_Average_R2']:.4f}")
    
    print(f"\nBasic CNN Average R²: {analysis['Model_Architecture_Analysis']['BasicCNN_Average_R2']:.4f}")
    print(f"Normalized CNN Average R²: {analysis['Model_Architecture_Analysis']['NormalizedCNN_Average_R2']:.4f}")
    
    print(f"\n🌟 Best Configuration:")
    print(f"{analysis['Best_Configuration']['Experiment']}")
    print(f"R² = {analysis['Best_Configuration']['R2_Score']:.4f}")
    
    print(f"\n💡 Key Findings:")
    for finding in analysis['Key_Findings']:
        print(f"  - {finding}")
    
    return df, analysis

if __name__ == "__main__":
    create_comparative_summary()
