import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
import pickle
import os
import json
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import matplotlib.pyplot as plt
import glob

# 设置matplotlib支持中文
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class BasicCNNRegressor(nn.Module):
    """基础CNN回归器 - 不使用BatchNorm"""
    
    def __init__(self, input_features=64):
        super(BasicCNNRegressor, self).__init__()
        
        # 1D卷积层 - 无归一化
        self.conv1 = nn.Conv1d(1, 32, kernel_size=3, padding=1)
        self.conv2 = nn.Conv1d(32, 64, kernel_size=3, padding=1)
        self.conv3 = nn.Conv1d(64, 32, kernel_size=3, padding=1)
        
        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool1d(1)
        
        # 全连接层
        self.fc1 = nn.Linear(32, 64)
        self.fc2 = nn.Linear(64, 32)
        self.fc3 = nn.Linear(32, 1)
        
        self.dropout = nn.Dropout(0.3)
        self.relu = nn.ReLU()
        
    def forward(self, x):
        x = x.unsqueeze(1)
        
        x = self.relu(self.conv1(x))
        x = self.dropout(x)
        x = self.relu(self.conv2(x))
        x = self.dropout(x)
        x = self.relu(self.conv3(x))
        
        x = self.global_pool(x).squeeze(-1)
        
        x = self.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.relu(self.fc2(x))
        x = torch.sigmoid(self.fc3(x))
        
        return x.squeeze(-1)


class NormalizedCNNRegressor(nn.Module):
    """归一化CNN回归器 - 使用BatchNorm"""
    
    def __init__(self, input_features=64):
        super(NormalizedCNNRegressor, self).__init__()
        
        self.feature_norm = nn.BatchNorm1d(input_features)
        
        # 1D卷积层 - 带归一化
        self.conv1 = nn.Conv1d(1, 32, kernel_size=3, padding=1)
        self.bn1 = nn.BatchNorm1d(32)
        self.conv2 = nn.Conv1d(32, 64, kernel_size=3, padding=1)
        self.bn2 = nn.BatchNorm1d(64)
        self.conv3 = nn.Conv1d(64, 32, kernel_size=3, padding=1)
        self.bn3 = nn.BatchNorm1d(32)
        
        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool1d(1)
        
        # 全连接层
        self.fc1 = nn.Linear(32, 64)
        self.fc2 = nn.Linear(64, 32)
        self.fc3 = nn.Linear(32, 1)
        
        self.dropout = nn.Dropout(0.3)
        self.relu = nn.ReLU()
        
    def forward(self, x):
        x = self.feature_norm(x)
        x = x.unsqueeze(1)
        
        x = self.relu(self.bn1(self.conv1(x)))
        x = self.dropout(x)
        x = self.relu(self.bn2(self.conv2(x)))
        x = self.dropout(x)
        x = self.relu(self.bn3(self.conv3(x)))
        
        x = self.global_pool(x).squeeze(-1)
        
        x = self.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.relu(self.fc2(x))
        x = torch.sigmoid(self.fc3(x))
        
        return x.squeeze(-1)


class ComparativeExperiment:
    """对比试验类"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.results = {}
        print(f"🔬 Comparative Experiment initialized on {self.device}")
    
    def load_data(self):
        """加载训练数据"""
        print("📂 Loading training data...")
        
        # 查找最新的训练数据文件
        data_files = glob.glob("simple_training_data_*.pkl")
        
        if not data_files:
            print("❌ No training data files found")
            return None, None
        
        # 使用最新的数据文件
        latest_file = max(data_files, key=os.path.getctime)
        print(f"📂 Using data file: {latest_file}")
        
        try:
            with open(latest_file, 'rb') as f:
                training_data = pickle.load(f)
            
            features = np.array(training_data['features'])
            labels = np.array(training_data['labels'])
            
            print(f"✅ Loaded {len(features)} samples")
            return features, labels
            
        except Exception as e:
            print(f"❌ Failed to load training data: {e}")
            return None, None
    
    def prepare_datasets(self, features, labels):
        """准备不同的数据集"""
        print("🔄 Preparing datasets...")
        
        # 1. 原始数据集（未处理）
        raw_features = features.copy()
        
        # 2. 标准化数据集（Z-score归一化）
        scaler_standard = StandardScaler()
        standardized_features = scaler_standard.fit_transform(features)
        
        # 3. 最小-最大归一化数据集
        scaler_minmax = MinMaxScaler()
        minmax_features = scaler_minmax.fit_transform(features)
        
        # 数据分割（使用相同的随机种子确保一致性）
        datasets = {}
        
        for name, data in [
            ('Raw', raw_features),
            ('StandardScaled', standardized_features),
            ('MinMaxScaled', minmax_features)
        ]:
            X_train, X_test, y_train, y_test = train_test_split(
                data, labels, test_size=0.2, random_state=42
            )
            
            datasets[name] = {
                'X_train': X_train,
                'X_test': X_test,
                'y_train': y_train,
                'y_test': y_test,
                'scaler': scaler_standard if name == 'StandardScaled' else (
                    scaler_minmax if name == 'MinMaxScaled' else None
                )
            }
            
            print(f"  📊 {name}: Train={len(X_train)}, Test={len(X_test)}")
        
        return datasets
    
    def train_model(self, model, X_train, y_train, X_test, y_test, model_name, epochs=200):
        """训练模型"""
        print(f"🤖 Training {model_name}...")
        
        # 转换为张量
        X_train_tensor = torch.FloatTensor(X_train).to(self.device)
        y_train_tensor = torch.FloatTensor(y_train).to(self.device)
        X_test_tensor = torch.FloatTensor(X_test).to(self.device)
        y_test_tensor = torch.FloatTensor(y_test).to(self.device)
        
        # 优化器和损失函数
        optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=0.01)
        criterion = nn.MSELoss()
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=15, factor=0.5)
        
        # 训练历史
        history = {
            'train_loss': [],
            'test_loss': [],
            'test_r2': []
        }
        
        best_r2 = -float('inf')
        patience_counter = 0
        patience = 25
        
        for epoch in range(epochs):
            # 训练阶段
            model.train()
            train_loss = 0
            
            # 批量训练
            batch_size = 32
            for i in range(0, len(X_train_tensor), batch_size):
                batch_X = X_train_tensor[i:i+batch_size]
                batch_y = y_train_tensor[i:i+batch_size]
                
                optimizer.zero_grad()
                outputs = model(batch_X)
                loss = criterion(outputs, batch_y)
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
            
            # 测试阶段
            model.eval()
            with torch.no_grad():
                test_outputs = model(X_test_tensor)
                test_loss = criterion(test_outputs, y_test_tensor).item()
                test_r2 = r2_score(y_test_tensor.cpu().numpy(), test_outputs.cpu().numpy())
            
            # 记录历史
            history['train_loss'].append(train_loss / (len(X_train_tensor) // batch_size))
            history['test_loss'].append(test_loss)
            history['test_r2'].append(test_r2)
            
            scheduler.step(test_loss)
            
            # 早停检查
            if test_r2 > best_r2:
                best_r2 = test_r2
                patience_counter = 0
                # 保存最佳模型
                best_model_state = model.state_dict().copy()
            else:
                patience_counter += 1
            
            # 打印进度
            if (epoch + 1) % 50 == 0:
                print(f"  Epoch {epoch+1}/{epochs}: Test R² = {test_r2:.4f}, Best = {best_r2:.4f}")
            
            # 早停
            if patience_counter >= patience:
                print(f"  🛑 Early stopping at epoch {epoch+1}")
                break
        
        # 加载最佳模型
        model.load_state_dict(best_model_state)
        
        # 最终评估
        model.eval()
        with torch.no_grad():
            final_predictions = model(X_test_tensor).cpu().numpy()
            final_true = y_test_tensor.cpu().numpy()
        
        # 计算最终指标
        final_metrics = {
            'r2_score': r2_score(final_true, final_predictions),
            'rmse': np.sqrt(mean_squared_error(final_true, final_predictions)),
            'mae': mean_absolute_error(final_true, final_predictions),
            'mape': np.mean(np.abs((final_true - final_predictions) / (final_true + 1e-8))) * 100,
            'correlation': np.corrcoef(final_true, final_predictions)[0, 1],
            'best_epoch': epoch + 1 - patience_counter,
            'training_history': history
        }
        
        print(f"  ✅ {model_name} completed: R² = {final_metrics['r2_score']:.4f}")
        
        return final_metrics, final_predictions, final_true
    
    def run_comparative_experiment(self):
        """运行对比试验"""
        print("🔬 Starting Comparative Experiment")
        print("=" * 70)
        
        # 加载数据
        features, labels = self.load_data()
        if features is None:
            return
        
        # 准备数据集
        datasets = self.prepare_datasets(features, labels)
        
        # 试验配置
        experiments = [
            {
                'name': 'Raw_Data_Basic_CNN',
                'dataset': 'Raw',
                'model_class': BasicCNNRegressor,
                'description': '原始数据 + 基础CNN（无BatchNorm）'
            },
            {
                'name': 'Raw_Data_Normalized_CNN',
                'dataset': 'Raw',
                'model_class': NormalizedCNNRegressor,
                'description': '原始数据 + 归一化CNN（有BatchNorm）'
            },
            {
                'name': 'StandardScaled_Data_Basic_CNN',
                'dataset': 'StandardScaled',
                'model_class': BasicCNNRegressor,
                'description': '标准化数据 + 基础CNN'
            },
            {
                'name': 'StandardScaled_Data_Normalized_CNN',
                'dataset': 'StandardScaled',
                'model_class': NormalizedCNNRegressor,
                'description': '标准化数据 + 归一化CNN'
            },
            {
                'name': 'MinMaxScaled_Data_Basic_CNN',
                'dataset': 'MinMaxScaled',
                'model_class': BasicCNNRegressor,
                'description': '最小-最大归一化数据 + 基础CNN'
            },
            {
                'name': 'MinMaxScaled_Data_Normalized_CNN',
                'dataset': 'MinMaxScaled',
                'model_class': NormalizedCNNRegressor,
                'description': '最小-最大归一化数据 + 归一化CNN'
            }
        ]
        
        # 运行所有试验
        for i, exp in enumerate(experiments, 1):
            print(f"\n🧪 Experiment {i}/6: {exp['description']}")
            print("-" * 50)
            
            # 获取数据
            dataset = datasets[exp['dataset']]
            
            # 创建模型
            model = exp['model_class'](64).to(self.device)
            
            # 训练模型
            metrics, predictions, true_values = self.train_model(
                model, 
                dataset['X_train'], 
                dataset['y_train'],
                dataset['X_test'], 
                dataset['y_test'],
                exp['name']
            )
            
            # 存储结果
            self.results[exp['name']] = {
                'description': exp['description'],
                'dataset_type': exp['dataset'],
                'model_type': exp['model_class'].__name__,
                'metrics': metrics,
                'predictions': predictions.tolist(),
                'true_values': true_values.tolist()
            }
        
        # 分析和保存结果
        self.analyze_results()
        self.save_results()
        self.plot_comparison()
    
    def analyze_results(self):
        """分析对比结果"""
        print("\n" + "=" * 80)
        print("📊 COMPARATIVE EXPERIMENT RESULTS ANALYSIS")
        print("=" * 80)
        
        # 创建结果表格
        results_table = []
        for name, result in self.results.items():
            metrics = result['metrics']
            results_table.append({
                'Experiment': result['description'],
                'Dataset': result['dataset_type'],
                'Model': result['model_type'],
                'R²': metrics['r2_score'],
                'RMSE': metrics['rmse'],
                'MAE': metrics['mae'],
                'MAPE': metrics['mape'],
                'Correlation': metrics['correlation']
            })
        
        # 按R²排序
        results_table.sort(key=lambda x: x['R²'], reverse=True)
        
        print(f"\n🏆 Performance Ranking (by R² Score):")
        print("-" * 80)
        for i, result in enumerate(results_table, 1):
            print(f"{i}. {result['Experiment']}")
            print(f"   R² = {result['R²']:.4f}, RMSE = {result['RMSE']:.4f}, MAE = {result['MAE']:.4f}")
        
        # 最佳结果
        best_result = results_table[0]
        print(f"\n🌟 Best Performance:")
        print(f"   {best_result['Experiment']}")
        print(f"   R² = {best_result['R²']:.4f}")
        
        # 数据预处理效果分析
        print(f"\n📈 Data Preprocessing Effect Analysis:")
        raw_results = [r for r in results_table if r['Dataset'] == 'Raw']
        std_results = [r for r in results_table if r['Dataset'] == 'StandardScaled']
        minmax_results = [r for r in results_table if r['Dataset'] == 'MinMaxScaled']
        
        print(f"   Raw Data Average R²: {np.mean([r['R²'] for r in raw_results]):.4f}")
        print(f"   StandardScaled Average R²: {np.mean([r['R²'] for r in std_results]):.4f}")
        print(f"   MinMaxScaled Average R²: {np.mean([r['R²'] for r in minmax_results]):.4f}")
        
        # 模型架构效果分析
        print(f"\n🤖 Model Architecture Effect Analysis:")
        basic_results = [r for r in results_table if r['Model'] == 'BasicCNNRegressor']
        norm_results = [r for r in results_table if r['Model'] == 'NormalizedCNNRegressor']
        
        print(f"   Basic CNN Average R²: {np.mean([r['R²'] for r in basic_results]):.4f}")
        print(f"   Normalized CNN Average R²: {np.mean([r['R²'] for r in norm_results]):.4f}")
    
    def save_results(self):
        """保存结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存详细结果
        results_filename = f"comparative_experiment_results_{timestamp}.json"
        with open(results_filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Results saved: {results_filename}")
        
        # 保存CSV摘要
        summary_data = []
        for name, result in self.results.items():
            metrics = result['metrics']
            summary_data.append({
                'Experiment_Name': name,
                'Description': result['description'],
                'Dataset_Type': result['dataset_type'],
                'Model_Type': result['model_type'],
                'R2_Score': metrics['r2_score'],
                'RMSE': metrics['rmse'],
                'MAE': metrics['mae'],
                'MAPE_Percent': metrics['mape'],
                'Correlation': metrics['correlation'],
                'Best_Epoch': metrics['best_epoch']
            })
        
        df = pd.DataFrame(summary_data)
        csv_filename = f"comparative_experiment_summary_{timestamp}.csv"
        df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
        
        print(f"📊 Summary CSV saved: {csv_filename}")
    
    def plot_comparison(self):
        """绘制对比图表"""
        try:
            # 提取R²分数
            names = []
            r2_scores = []
            
            for name, result in self.results.items():
                names.append(name.replace('_', '\n'))
                r2_scores.append(result['metrics']['r2_score'])
            
            # 创建对比图
            plt.figure(figsize=(15, 8))
            
            # 柱状图
            bars = plt.bar(range(len(names)), r2_scores, alpha=0.7)
            
            # 颜色编码
            colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown']
            for bar, color in zip(bars, colors):
                bar.set_color(color)
            
            plt.xlabel('Experiment Configuration')
            plt.ylabel('R² Score')
            plt.title('Comparative Experiment Results: R² Score Comparison')
            plt.xticks(range(len(names)), names, rotation=45, ha='right')
            plt.grid(True, alpha=0.3)
            
            # 添加数值标签
            for i, score in enumerate(r2_scores):
                plt.text(i, score + 0.01, f'{score:.3f}', ha='center', va='bottom')
            
            plt.tight_layout()
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            plot_filename = f"comparative_experiment_plot_{timestamp}.png"
            plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
            plt.show()
            
            print(f"📊 Comparison plot saved: {plot_filename}")
            
        except Exception as e:
            print(f"⚠️ Plotting failed: {e}")


def main():
    """主函数"""
    print("🔬 CNN Model Comparative Experiment")
    print("🎯 Comparing Raw vs Normalized Data & Basic vs Normalized CNN")
    print("=" * 70)
    
    try:
        # 创建并运行对比试验
        experiment = ComparativeExperiment()
        experiment.run_comparative_experiment()
        
        print(f"\n✅ Comparative experiment completed successfully!")
        print(f"🎉 Check the generated files for detailed analysis!")
        
    except Exception as e:
        print(f"❌ Experiment failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
