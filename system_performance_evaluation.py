import torch
import cv2
import os
import numpy as np
import time
import json
import pandas as pd
from datetime import datetime
from collections import defaultdict
import matplotlib.pyplot as plt
from ultralytics import YOLO
from deep_sort.deep_sort import DeepSort
from deep_sort.configs.parser import get_config

# 设置matplotlib支持中文
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class SystemPerformanceEvaluator:
    """
    整个系统性能评估器
    评估YOLOv11-seg + DeepSORT + CNN回归器的端到端性能
    """
    
    def __init__(self, config):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.config = config
        
        # 性能指标存储
        self.performance_metrics = {
            'detection_metrics': {},
            'tracking_metrics': {},
            'segmentation_metrics': {},
            'cnn_prediction_metrics': {},
            'system_metrics': {},
            'timing_metrics': {}
        }
        
        # 初始化各个模块
        self.init_yolo_model()
        self.init_deepsort()
        self.init_cnn_regressor()
        
        print(f"🔬 System Performance Evaluator initialized")
        print(f"🔧 Device: {self.device}")
    
    def init_yolo_model(self):
        """初始化YOLO模型"""
        try:
            self.yolo_model = YOLO(self.config["weights"]).to(self.device)
            self.class_names = self.yolo_model.names
            print(f"✅ YOLO model loaded: {self.config['weights']}")
        except Exception as e:
            print(f"❌ Failed to load YOLO model: {e}")
            self.yolo_model = None
    
    def init_deepsort(self):
        """初始化DeepSORT"""
        try:
            cfg = get_config()
            cfg.merge_from_file("deep_sort/configs/deep_sort.yaml")
            
            cfg.DEEPSORT.MAX_DIST = 0.3
            cfg.DEEPSORT.MIN_CONFIDENCE = 0.4
            cfg.DEEPSORT.NMS_MAX_OVERLAP = 0.8
            cfg.DEEPSORT.MAX_IOU_DISTANCE = 0.9
            cfg.DEEPSORT.MAX_AGE = 50
            cfg.DEEPSORT.N_INIT = 5
            cfg.DEEPSORT.NN_BUDGET = 150
            
            self.tracker = DeepSort(
                model_path=cfg.DEEPSORT.REID_CKPT,
                max_dist=cfg.DEEPSORT.MAX_DIST,
                min_confidence=cfg.DEEPSORT.MIN_CONFIDENCE,
                nms_max_overlap=cfg.DEEPSORT.NMS_MAX_OVERLAP,
                max_iou_distance=cfg.DEEPSORT.MAX_IOU_DISTANCE,
                max_age=cfg.DEEPSORT.MAX_AGE,
                n_init=cfg.DEEPSORT.N_INIT,
                nn_budget=cfg.DEEPSORT.NN_BUDGET,
                use_cuda=torch.cuda.is_available()
            )
            print(f"✅ DeepSORT initialized")
        except Exception as e:
            print(f"❌ Failed to initialize DeepSORT: {e}")
            self.tracker = None
    
    def init_cnn_regressor(self):
        """初始化CNN回归器"""
        try:
            from v20_enhanced_cnn_airflow_system import EnhancedCNNRegressor, EnhancedFeatureExtractor
            
            model_path = "models/improved_cnn_regressor.pth"
            if os.path.exists(model_path):
                checkpoint = torch.load(model_path, map_location=self.device, weights_only=False)
                self.cnn_regressor = EnhancedCNNRegressor(64).to(self.device)
                self.cnn_regressor.load_state_dict(checkpoint['model_state_dict'])
                self.cnn_regressor.eval()
                
                self.feature_extractor = EnhancedFeatureExtractor()
                print(f"✅ CNN regressor loaded")
            else:
                print(f"❌ CNN model not found: {model_path}")
                self.cnn_regressor = None
                self.feature_extractor = None
        except Exception as e:
            print(f"❌ Failed to load CNN regressor: {e}")
            self.cnn_regressor = None
            self.feature_extractor = None
    
    def evaluate_detection_performance(self, frame, ground_truth_boxes=None):
        """评估检测性能"""
        start_time = time.time()
        
        # YOLO检测
        results = self.yolo_model.predict(
            source=frame,
            imgsz=640,
            conf=0.5,
            iou=0.5,
            device=self.device,
            verbose=False
        )
        
        detection_time = time.time() - start_time
        
        # 提取检测结果
        detections = []
        segmentation_masks = []
        
        for result in results:
            if result.boxes is not None:
                boxes = result.boxes.xywh.cpu().numpy()
                confs = result.boxes.conf.cpu().numpy()
                clss = result.boxes.cls.cpu().numpy().astype(int)
                
                for box, conf, cls_id in zip(boxes, confs, clss):
                    detections.append({
                        'bbox': box,
                        'confidence': conf,
                        'class_id': cls_id,
                        'class_name': self.class_names[cls_id]
                    })
            
            if result.masks is not None:
                masks = result.masks.data.cpu().numpy()
                segmentation_masks.extend(masks)
        
        # 计算检测指标
        detection_metrics = {
            'detection_count': len(detections),
            'strawberry_count': len([d for d in detections if d['class_id'] == 0]),
            'obstacle_count': len([d for d in detections if d['class_id'] == 2]),
            'avg_confidence': np.mean([d['confidence'] for d in detections]) if detections else 0,
            'detection_time': detection_time,
            'fps': 1.0 / detection_time if detection_time > 0 else 0
        }
        
        return detection_metrics, detections, segmentation_masks
    
    def evaluate_tracking_performance(self, detections, frame_id, frame):
        """评估跟踪性能"""
        start_time = time.time()

        # 准备DeepSORT输入
        if detections and self.tracker:
            # 转换检测格式
            det_boxes = []
            det_confs = []
            det_classes = []

            for det in detections:
                if det['class_id'] == 0:  # 只跟踪草莓
                    bbox = det['bbox']
                    # DeepSORT需要xywh格式
                    det_boxes.append(bbox)
                    det_confs.append(det['confidence'])
                    det_classes.append(det['class_id'])

            if det_boxes:
                det_boxes = np.array(det_boxes)
                det_confs = np.array(det_confs)
                det_classes = np.array(det_classes)

                # DeepSORT跟踪 - 传入原始图像
                tracks = self.tracker.update(det_boxes, det_confs, det_classes, frame)

                tracking_time = time.time() - start_time

                # 计算跟踪指标
                tracking_metrics = {
                    'track_count': len(tracks),
                    'new_tracks': len([t for t in tracks if t.time_since_update == 0]),
                    'lost_tracks': 0,  # 需要跨帧统计
                    'tracking_time': tracking_time,
                    'tracking_fps': 1.0 / tracking_time if tracking_time > 0 else 0
                }

                return tracking_metrics, tracks
            else:
                return {'track_count': 0, 'tracking_time': 0, 'tracking_fps': 0}, []
        else:
            return {'track_count': 0, 'tracking_time': 0, 'tracking_fps': 0}, []
    
    def evaluate_segmentation_quality(self, masks, frame_shape):
        """评估分割质量"""
        if not masks:
            return {'mask_count': 0, 'avg_mask_area': 0, 'mask_coverage': 0}
        
        total_area = frame_shape[0] * frame_shape[1]
        mask_areas = []
        total_mask_area = 0
        
        for mask in masks:
            if mask.shape[0] != frame_shape[0] or mask.shape[1] != frame_shape[1]:
                mask = cv2.resize(mask, (frame_shape[1], frame_shape[0]))
            
            mask_area = np.sum(mask > 0.5)
            mask_areas.append(mask_area)
            total_mask_area += mask_area
        
        segmentation_metrics = {
            'mask_count': len(masks),
            'avg_mask_area': np.mean(mask_areas) if mask_areas else 0,
            'total_mask_area': total_mask_area,
            'mask_coverage': total_mask_area / total_area,
            'mask_area_std': np.std(mask_areas) if mask_areas else 0
        }
        
        return segmentation_metrics
    
    def evaluate_cnn_prediction(self, frame, strawberry_mask, obstacle_mask):
        """评估CNN预测性能"""
        if self.cnn_regressor is None or self.feature_extractor is None:
            return {'prediction_time': 0, 'prediction_value': 0, 'feature_extraction_success': False}
        
        start_time = time.time()
        
        try:
            # 特征提取
            features = self.feature_extractor.extract_simple_features(
                frame, strawberry_mask, obstacle_mask
            )
            
            if features is None:
                return {'prediction_time': 0, 'prediction_value': 0, 'feature_extraction_success': False}
            
            # CNN预测
            features_tensor = torch.FloatTensor(features).unsqueeze(0).to(self.device)
            
            with torch.no_grad():
                prediction = self.cnn_regressor(features_tensor).item()
            
            prediction_time = time.time() - start_time
            
            cnn_metrics = {
                'prediction_time': prediction_time,
                'prediction_value': prediction,
                'feature_extraction_success': True,
                'feature_dimension': len(features),
                'prediction_fps': 1.0 / prediction_time if prediction_time > 0 else 0
            }
            
            return cnn_metrics
            
        except Exception as e:
            print(f"⚠️ CNN prediction failed: {e}")
            return {'prediction_time': 0, 'prediction_value': 0, 'feature_extraction_success': False}
    
    def evaluate_video_performance(self, video_path, max_frames=None):
        """评估整个视频的系统性能"""
        print(f"🎬 Evaluating system performance on: {os.path.basename(video_path)}")
        
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print(f"❌ Cannot open video: {video_path}")
            return None
        
        # 视频信息
        width, height = int(cap.get(3)), int(cap.get(4))
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        if max_frames:
            total_frames = min(total_frames, max_frames)
        
        print(f"📹 Video info: {width}x{height}, {fps}fps, processing {total_frames} frames")
        
        # 性能统计
        frame_metrics = []
        system_start_time = time.time()
        
        frame_count = 0
        
        while cap.isOpened() and frame_count < total_frames:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            frame_start_time = time.time()
            
            # 1. 检测性能评估
            detection_metrics, detections, masks = self.evaluate_detection_performance(frame)
            
            # 2. 跟踪性能评估
            tracking_metrics, tracks = self.evaluate_tracking_performance(detections, frame_count, frame)
            
            # 3. 分割质量评估
            segmentation_metrics = self.evaluate_segmentation_quality(masks, frame.shape[:2])
            
            # 4. CNN预测性能评估
            cnn_metrics_list = []
            if masks and len(masks) >= 2:  # 需要草莓和遮挡掩膜
                strawberry_mask = (masks[0] * 255).astype(np.uint8) if len(masks) > 0 else np.zeros_like(frame[:,:,0])
                obstacle_mask = (masks[1] * 255).astype(np.uint8) if len(masks) > 1 else np.zeros_like(frame[:,:,0])
                
                # 调整掩膜尺寸
                if strawberry_mask.shape != frame.shape[:2]:
                    strawberry_mask = cv2.resize(strawberry_mask, (width, height))
                if obstacle_mask.shape != frame.shape[:2]:
                    obstacle_mask = cv2.resize(obstacle_mask, (width, height))
                
                cnn_metrics = self.evaluate_cnn_prediction(frame, strawberry_mask, obstacle_mask)
                cnn_metrics_list.append(cnn_metrics)
            
            # 计算帧总处理时间
            frame_total_time = time.time() - frame_start_time
            
            # 存储帧指标
            frame_metric = {
                'frame_id': frame_count,
                'frame_time': frame_total_time,
                'frame_fps': 1.0 / frame_total_time if frame_total_time > 0 else 0,
                'detection': detection_metrics,
                'tracking': tracking_metrics,
                'segmentation': segmentation_metrics,
                'cnn_predictions': cnn_metrics_list
            }
            
            frame_metrics.append(frame_metric)
            
            # 进度显示
            if frame_count % 50 == 0:
                progress = (frame_count / total_frames) * 100
                avg_fps = frame_count / (time.time() - system_start_time)
                print(f"  📊 Progress: {frame_count}/{total_frames} ({progress:.1f}%), Avg FPS: {avg_fps:.1f}")
        
        cap.release()
        
        total_system_time = time.time() - system_start_time
        
        # 计算整体系统指标
        system_metrics = self.calculate_system_metrics(frame_metrics, total_system_time, total_frames)
        
        # 保存结果
        evaluation_result = {
            'video_info': {
                'path': video_path,
                'width': width,
                'height': height,
                'fps': fps,
                'total_frames': total_frames,
                'processed_frames': frame_count
            },
            'system_metrics': system_metrics,
            'frame_metrics': frame_metrics
        }
        
        return evaluation_result
    
    def calculate_system_metrics(self, frame_metrics, total_time, total_frames):
        """计算整体系统指标"""
        # 提取各模块指标
        detection_times = [f['detection']['detection_time'] for f in frame_metrics]
        tracking_times = [f['tracking']['tracking_time'] for f in frame_metrics]
        frame_times = [f['frame_time'] for f in frame_metrics]
        
        detection_counts = [f['detection']['detection_count'] for f in frame_metrics]
        strawberry_counts = [f['detection']['strawberry_count'] for f in frame_metrics]
        track_counts = [f['tracking']['track_count'] for f in frame_metrics]
        
        # CNN预测指标
        cnn_times = []
        cnn_predictions = []
        successful_predictions = 0
        
        for f in frame_metrics:
            for cnn_pred in f['cnn_predictions']:
                if cnn_pred['feature_extraction_success']:
                    cnn_times.append(cnn_pred['prediction_time'])
                    cnn_predictions.append(cnn_pred['prediction_value'])
                    successful_predictions += 1
        
        # 计算系统指标
        system_metrics = {
            'overall_performance': {
                'total_processing_time': total_time,
                'total_frames': total_frames,
                'average_fps': total_frames / total_time if total_time > 0 else 0,
                'real_time_capability': (total_frames / total_time) >= 25 if total_time > 0 else False
            },
            'detection_performance': {
                'avg_detection_time': np.mean(detection_times),
                'detection_fps': 1.0 / np.mean(detection_times) if detection_times else 0,
                'total_detections': sum(detection_counts),
                'total_strawberries': sum(strawberry_counts),
                'avg_detections_per_frame': np.mean(detection_counts),
                'detection_stability': np.std(detection_counts)
            },
            'tracking_performance': {
                'avg_tracking_time': np.mean(tracking_times),
                'tracking_fps': 1.0 / np.mean(tracking_times) if tracking_times else 0,
                'total_tracks': sum(track_counts),
                'avg_tracks_per_frame': np.mean(track_counts),
                'tracking_stability': np.std(track_counts)
            },
            'cnn_performance': {
                'avg_prediction_time': np.mean(cnn_times) if cnn_times else 0,
                'prediction_fps': 1.0 / np.mean(cnn_times) if cnn_times else 0,
                'successful_predictions': successful_predictions,
                'prediction_success_rate': successful_predictions / total_frames if total_frames > 0 else 0,
                'avg_prediction_value': np.mean(cnn_predictions) if cnn_predictions else 0,
                'prediction_std': np.std(cnn_predictions) if cnn_predictions else 0
            },
            'system_efficiency': {
                'detection_time_ratio': np.mean(detection_times) / np.mean(frame_times) if frame_times else 0,
                'tracking_time_ratio': np.mean(tracking_times) / np.mean(frame_times) if frame_times else 0,
                'cnn_time_ratio': np.mean(cnn_times) / np.mean(frame_times) if cnn_times and frame_times else 0,
                'bottleneck_module': self.identify_bottleneck(detection_times, tracking_times, cnn_times)
            }
        }
        
        return system_metrics
    
    def identify_bottleneck(self, detection_times, tracking_times, cnn_times):
        """识别系统瓶颈"""
        avg_times = {
            'detection': np.mean(detection_times) if detection_times else 0,
            'tracking': np.mean(tracking_times) if tracking_times else 0,
            'cnn': np.mean(cnn_times) if cnn_times else 0
        }
        
        return max(avg_times.items(), key=lambda x: x[1])[0]

    def save_evaluation_results(self, results, output_dir="evaluation_results"):
        """保存评估结果"""
        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # 保存完整结果
        results_file = os.path.join(output_dir, f"system_evaluation_{timestamp}.json")

        # 处理不能序列化的对象
        serializable_results = self.make_serializable(results)

        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, indent=2, ensure_ascii=False)

        # 保存CSV摘要
        summary_data = self.create_summary_dataframe(results)
        csv_file = os.path.join(output_dir, f"system_summary_{timestamp}.csv")
        summary_data.to_csv(csv_file, index=False, encoding='utf-8-sig')

        print(f"💾 Evaluation results saved:")
        print(f"  📄 Full results: {results_file}")
        print(f"  📊 Summary CSV: {csv_file}")

        return results_file, csv_file

    def make_serializable(self, obj):
        """将结果转换为可序列化格式"""
        if isinstance(obj, dict):
            return {k: self.make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self.make_serializable(item) for item in obj]
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.int64, np.int32)):
            return int(obj)
        elif isinstance(obj, (np.float64, np.float32)):
            return float(obj)
        else:
            return obj

    def create_summary_dataframe(self, results):
        """创建摘要数据框"""
        system_metrics = results['system_metrics']
        video_info = results['video_info']

        summary = {
            'Video_Name': [os.path.basename(video_info['path'])],
            'Total_Frames': [video_info['processed_frames']],
            'Processing_Time_s': [system_metrics['overall_performance']['total_processing_time']],
            'Average_FPS': [system_metrics['overall_performance']['average_fps']],
            'Real_Time_Capable': [system_metrics['overall_performance']['real_time_capability']],
            'Detection_FPS': [system_metrics['detection_performance']['detection_fps']],
            'Tracking_FPS': [system_metrics['tracking_performance']['tracking_fps']],
            'CNN_FPS': [system_metrics['cnn_performance']['prediction_fps']],
            'Total_Strawberries': [system_metrics['detection_performance']['total_strawberries']],
            'Total_Tracks': [system_metrics['tracking_performance']['total_tracks']],
            'CNN_Success_Rate': [system_metrics['cnn_performance']['prediction_success_rate']],
            'Bottleneck_Module': [system_metrics['system_efficiency']['bottleneck_module']],
            'Detection_Time_Ratio': [system_metrics['system_efficiency']['detection_time_ratio']],
            'Tracking_Time_Ratio': [system_metrics['system_efficiency']['tracking_time_ratio']],
            'CNN_Time_Ratio': [system_metrics['system_efficiency']['cnn_time_ratio']]
        }

        return pd.DataFrame(summary)

    def plot_performance_analysis(self, results, output_dir="evaluation_results"):
        """绘制性能分析图表"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

            # 提取时间序列数据
            frame_ids = [f['frame_id'] for f in results['frame_metrics']]
            frame_fps = [f['frame_fps'] for f in results['frame_metrics']]
            detection_times = [f['detection']['detection_time'] for f in results['frame_metrics']]
            tracking_times = [f['tracking']['tracking_time'] for f in results['frame_metrics']]

            # CNN预测时间
            cnn_times = []
            for f in results['frame_metrics']:
                if f['cnn_predictions']:
                    cnn_times.append(f['cnn_predictions'][0]['prediction_time'])
                else:
                    cnn_times.append(0)

            # 创建多子图
            fig, axes = plt.subplots(2, 3, figsize=(18, 12))

            # 1. 整体FPS趋势
            axes[0, 0].plot(frame_ids, frame_fps, 'b-', alpha=0.7, linewidth=1)
            axes[0, 0].axhline(y=25, color='r', linestyle='--', label='Real-time threshold (25 FPS)')
            axes[0, 0].set_xlabel('Frame ID')
            axes[0, 0].set_ylabel('FPS')
            axes[0, 0].set_title('System FPS Over Time')
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)

            # 2. 各模块处理时间对比
            module_times = [
                np.mean(detection_times),
                np.mean(tracking_times),
                np.mean(cnn_times)
            ]
            module_names = ['Detection', 'Tracking', 'CNN']

            bars = axes[0, 1].bar(module_names, module_times, alpha=0.7,
                                 color=['blue', 'green', 'orange'])
            axes[0, 1].set_ylabel('Average Time (s)')
            axes[0, 1].set_title('Module Processing Time Comparison')
            axes[0, 1].grid(True, alpha=0.3, axis='y')

            # 添加数值标签
            for bar, time_val in zip(bars, module_times):
                axes[0, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,
                               f'{time_val:.3f}s', ha='center', va='bottom')

            # 3. 检测性能统计
            detection_counts = [f['detection']['detection_count'] for f in results['frame_metrics']]
            strawberry_counts = [f['detection']['strawberry_count'] for f in results['frame_metrics']]

            axes[0, 2].plot(frame_ids, detection_counts, 'b-', label='Total Detections', alpha=0.7)
            axes[0, 2].plot(frame_ids, strawberry_counts, 'r-', label='Strawberries', alpha=0.7)
            axes[0, 2].set_xlabel('Frame ID')
            axes[0, 2].set_ylabel('Count')
            axes[0, 2].set_title('Detection Count Over Time')
            axes[0, 2].legend()
            axes[0, 2].grid(True, alpha=0.3)

            # 4. 跟踪性能
            track_counts = [f['tracking']['track_count'] for f in results['frame_metrics']]

            axes[1, 0].plot(frame_ids, track_counts, 'g-', alpha=0.7, linewidth=1)
            axes[1, 0].set_xlabel('Frame ID')
            axes[1, 0].set_ylabel('Active Tracks')
            axes[1, 0].set_title('Tracking Performance Over Time')
            axes[1, 0].grid(True, alpha=0.3)

            # 5. CNN预测值分布
            cnn_predictions = []
            for f in results['frame_metrics']:
                for pred in f['cnn_predictions']:
                    if pred['feature_extraction_success']:
                        cnn_predictions.append(pred['prediction_value'])

            if cnn_predictions:
                axes[1, 1].hist(cnn_predictions, bins=20, alpha=0.7, color='purple', edgecolor='black')
                axes[1, 1].set_xlabel('Predicted Residual Occlusion Rate')
                axes[1, 1].set_ylabel('Frequency')
                axes[1, 1].set_title('CNN Prediction Distribution')
                axes[1, 1].grid(True, alpha=0.3)

            # 6. 系统效率饼图
            system_metrics = results['system_metrics']
            time_ratios = [
                system_metrics['system_efficiency']['detection_time_ratio'],
                system_metrics['system_efficiency']['tracking_time_ratio'],
                system_metrics['system_efficiency']['cnn_time_ratio']
            ]

            # 计算其他时间占比
            other_ratio = max(0, 1 - sum(time_ratios))
            time_ratios.append(other_ratio)
            labels = ['Detection', 'Tracking', 'CNN', 'Other']
            colors = ['blue', 'green', 'orange', 'gray']

            axes[1, 2].pie(time_ratios, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
            axes[1, 2].set_title('Processing Time Distribution')

            plt.tight_layout()

            # 保存图表
            plot_file = os.path.join(output_dir, f"performance_analysis_{timestamp}.png")
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            plt.show()

            print(f"📊 Performance analysis plot saved: {plot_file}")

        except Exception as e:
            print(f"⚠️ Plotting failed: {e}")

    def print_performance_summary(self, results):
        """打印性能摘要"""
        system_metrics = results['system_metrics']
        video_info = results['video_info']

        print("\n" + "=" * 80)
        print("🎯 SYSTEM PERFORMANCE EVALUATION SUMMARY")
        print("=" * 80)

        print(f"📹 Video: {os.path.basename(video_info['path'])}")
        print(f"📊 Frames: {video_info['processed_frames']}/{video_info['total_frames']}")
        print(f"⏱️ Total Time: {system_metrics['overall_performance']['total_processing_time']:.2f}s")
        print(f"🚀 Average FPS: {system_metrics['overall_performance']['average_fps']:.1f}")
        print(f"⚡ Real-time: {'✅ Yes' if system_metrics['overall_performance']['real_time_capability'] else '❌ No'}")

        print(f"\n🔍 Detection Performance:")
        det_perf = system_metrics['detection_performance']
        print(f"  FPS: {det_perf['detection_fps']:.1f}")
        print(f"  Total Strawberries: {det_perf['total_strawberries']}")
        print(f"  Avg per Frame: {det_perf['avg_detections_per_frame']:.1f}")

        print(f"\n🎯 Tracking Performance:")
        track_perf = system_metrics['tracking_performance']
        print(f"  FPS: {track_perf['tracking_fps']:.1f}")
        print(f"  Total Tracks: {track_perf['total_tracks']}")
        print(f"  Avg per Frame: {track_perf['avg_tracks_per_frame']:.1f}")

        print(f"\n🤖 CNN Performance:")
        cnn_perf = system_metrics['cnn_performance']
        print(f"  FPS: {cnn_perf['prediction_fps']:.1f}")
        print(f"  Success Rate: {cnn_perf['prediction_success_rate']:.1%}")
        print(f"  Avg Prediction: {cnn_perf['avg_prediction_value']:.3f}")

        print(f"\n⚙️ System Efficiency:")
        efficiency = system_metrics['system_efficiency']
        print(f"  Bottleneck: {efficiency['bottleneck_module']}")
        print(f"  Detection: {efficiency['detection_time_ratio']:.1%}")
        print(f"  Tracking: {efficiency['tracking_time_ratio']:.1%}")
        print(f"  CNN: {efficiency['cnn_time_ratio']:.1%}")

        # 性能评级
        avg_fps = system_metrics['overall_performance']['average_fps']
        if avg_fps >= 30:
            rating = "🌟 Excellent"
        elif avg_fps >= 25:
            rating = "✅ Good (Real-time)"
        elif avg_fps >= 15:
            rating = "🟡 Fair"
        else:
            rating = "🔴 Poor"

        print(f"\n🏆 Overall Rating: {rating}")


def main():
    """主函数"""
    print("🔬 System Performance Evaluation")
    print("🎯 Evaluating YOLOv11-seg + DeepSORT + CNN Regressor")
    print("=" * 70)

    # 配置
    config = {
        "weights": "weights/best.pt",
        "input": "视频/1.mp4",
        "output": "output2/system_evaluation_result.mp4"
    }

    try:
        # 创建评估器
        evaluator = SystemPerformanceEvaluator(config)

        # 评估视频性能
        video_path = config["input"]
        results = evaluator.evaluate_video_performance(video_path, max_frames=200)  # 限制帧数以加快测试

        if results:
            # 打印摘要
            evaluator.print_performance_summary(results)

            # 保存结果
            evaluator.save_evaluation_results(results)

            # 绘制分析图表
            evaluator.plot_performance_analysis(results)

            print(f"\n✅ System evaluation completed successfully!")
        else:
            print(f"❌ System evaluation failed!")

    except Exception as e:
        print(f"❌ Evaluation failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
