import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
import pickle
import os
import json
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
import matplotlib.pyplot as plt
import glob

# 设置matplotlib支持中文
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class BaselineModel(nn.Module):
    """基线模型 - 最简单的全连接网络"""
    
    def __init__(self, input_features=64):
        super(BaselineModel, self).__init__()
        self.fc1 = nn.Linear(input_features, 32)
        self.fc2 = nn.Linear(32, 16)
        self.fc3 = nn.Linear(16, 1)
        self.relu = nn.ReLU()
        
    def forward(self, x):
        x = self.relu(self.fc1(x))
        x = self.relu(self.fc2(x))
        x = torch.sigmoid(self.fc3(x))
        return x.squeeze(-1)


class BaselineWithDropout(nn.Module):
    """基线模型 + Dropout"""
    
    def __init__(self, input_features=64):
        super(BaselineWithDropout, self).__init__()
        self.fc1 = nn.Linear(input_features, 32)
        self.fc2 = nn.Linear(32, 16)
        self.fc3 = nn.Linear(16, 1)
        self.dropout = nn.Dropout(0.3)
        self.relu = nn.ReLU()
        
    def forward(self, x):
        x = self.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.relu(self.fc2(x))
        x = self.dropout(x)
        x = torch.sigmoid(self.fc3(x))
        return x.squeeze(-1)


class CNNWithoutNorm(nn.Module):
    """CNN模型（无归一化）"""
    
    def __init__(self, input_features=64):
        super(CNNWithoutNorm, self).__init__()
        
        # 1D卷积层
        self.conv1 = nn.Conv1d(1, 32, kernel_size=3, padding=1)
        self.conv2 = nn.Conv1d(32, 64, kernel_size=3, padding=1)
        self.conv3 = nn.Conv1d(64, 32, kernel_size=3, padding=1)
        
        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool1d(1)
        
        # 全连接层
        self.fc1 = nn.Linear(32, 64)
        self.fc2 = nn.Linear(64, 32)
        self.fc3 = nn.Linear(32, 1)
        
        self.dropout = nn.Dropout(0.3)
        self.relu = nn.ReLU()
        
    def forward(self, x):
        x = x.unsqueeze(1)
        
        x = self.relu(self.conv1(x))
        x = self.dropout(x)
        x = self.relu(self.conv2(x))
        x = self.dropout(x)
        x = self.relu(self.conv3(x))
        
        x = self.global_pool(x).squeeze(-1)
        
        x = self.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.relu(self.fc2(x))
        x = torch.sigmoid(self.fc3(x))
        
        return x.squeeze(-1)


class CNNWithInputNorm(nn.Module):
    """CNN模型 + 输入归一化"""
    
    def __init__(self, input_features=64):
        super(CNNWithInputNorm, self).__init__()
        
        self.input_norm = nn.BatchNorm1d(input_features)
        
        # 1D卷积层
        self.conv1 = nn.Conv1d(1, 32, kernel_size=3, padding=1)
        self.conv2 = nn.Conv1d(32, 64, kernel_size=3, padding=1)
        self.conv3 = nn.Conv1d(64, 32, kernel_size=3, padding=1)
        
        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool1d(1)
        
        # 全连接层
        self.fc1 = nn.Linear(32, 64)
        self.fc2 = nn.Linear(64, 32)
        self.fc3 = nn.Linear(32, 1)
        
        self.dropout = nn.Dropout(0.3)
        self.relu = nn.ReLU()
        
    def forward(self, x):
        x = self.input_norm(x)
        x = x.unsqueeze(1)
        
        x = self.relu(self.conv1(x))
        x = self.dropout(x)
        x = self.relu(self.conv2(x))
        x = self.dropout(x)
        x = self.relu(self.conv3(x))
        
        x = self.global_pool(x).squeeze(-1)
        
        x = self.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.relu(self.fc2(x))
        x = torch.sigmoid(self.fc3(x))
        
        return x.squeeze(-1)


class CNNWithConvNorm(nn.Module):
    """CNN模型 + 卷积层归一化"""
    
    def __init__(self, input_features=64):
        super(CNNWithConvNorm, self).__init__()
        
        self.input_norm = nn.BatchNorm1d(input_features)
        
        # 1D卷积层 + BatchNorm
        self.conv1 = nn.Conv1d(1, 32, kernel_size=3, padding=1)
        self.bn1 = nn.BatchNorm1d(32)
        self.conv2 = nn.Conv1d(32, 64, kernel_size=3, padding=1)
        self.bn2 = nn.BatchNorm1d(64)
        self.conv3 = nn.Conv1d(64, 32, kernel_size=3, padding=1)
        self.bn3 = nn.BatchNorm1d(32)
        
        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool1d(1)
        
        # 全连接层
        self.fc1 = nn.Linear(32, 64)
        self.fc2 = nn.Linear(64, 32)
        self.fc3 = nn.Linear(32, 1)
        
        self.dropout = nn.Dropout(0.3)
        self.relu = nn.ReLU()
        
    def forward(self, x):
        x = self.input_norm(x)
        x = x.unsqueeze(1)
        
        x = self.relu(self.bn1(self.conv1(x)))
        x = self.dropout(x)
        x = self.relu(self.bn2(self.conv2(x)))
        x = self.dropout(x)
        x = self.relu(self.bn3(self.conv3(x)))
        
        x = self.global_pool(x).squeeze(-1)
        
        x = self.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.relu(self.fc2(x))
        x = torch.sigmoid(self.fc3(x))
        
        return x.squeeze(-1)


class FullModel(nn.Module):
    """完整模型 - 所有模块"""
    
    def __init__(self, input_features=64):
        super(FullModel, self).__init__()
        
        self.input_norm = nn.BatchNorm1d(input_features)
        
        # 1D卷积层 + BatchNorm
        self.conv1 = nn.Conv1d(1, 32, kernel_size=3, padding=1)
        self.bn1 = nn.BatchNorm1d(32)
        self.conv2 = nn.Conv1d(32, 64, kernel_size=3, padding=1)
        self.bn2 = nn.BatchNorm1d(64)
        self.conv3 = nn.Conv1d(64, 32, kernel_size=3, padding=1)
        self.bn3 = nn.BatchNorm1d(32)
        
        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool1d(1)
        
        # 全连接层 + BatchNorm
        self.fc1 = nn.Linear(32, 64)
        self.fc_bn1 = nn.BatchNorm1d(64)
        self.fc2 = nn.Linear(64, 32)
        self.fc_bn2 = nn.BatchNorm1d(32)
        self.fc3 = nn.Linear(32, 1)
        
        self.dropout = nn.Dropout(0.3)
        self.relu = nn.ReLU()
        
    def forward(self, x):
        x = self.input_norm(x)
        x = x.unsqueeze(1)
        
        x = self.relu(self.bn1(self.conv1(x)))
        x = self.dropout(x)
        x = self.relu(self.bn2(self.conv2(x)))
        x = self.dropout(x)
        x = self.relu(self.bn3(self.conv3(x)))
        
        x = self.global_pool(x).squeeze(-1)
        
        x = self.relu(self.fc_bn1(self.fc1(x)))
        x = self.dropout(x)
        x = self.relu(self.fc_bn2(self.fc2(x)))
        x = self.dropout(x)
        x = torch.sigmoid(self.fc3(x))
        
        return x.squeeze(-1)


class AblationStudy:
    """消融试验类"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.results = {}
        print(f"🔬 Ablation Study initialized on {self.device}")
    
    def load_data(self):
        """加载训练数据"""
        print("📂 Loading training data...")
        
        # 查找最新的训练数据文件
        data_files = glob.glob("simple_training_data_*.pkl")
        
        if not data_files:
            print("❌ No training data files found")
            return None, None
        
        # 使用最新的数据文件
        latest_file = max(data_files, key=os.path.getctime)
        print(f"📂 Using data file: {latest_file}")
        
        try:
            with open(latest_file, 'rb') as f:
                training_data = pickle.load(f)
            
            features = np.array(training_data['features'])
            labels = np.array(training_data['labels'])
            
            print(f"✅ Loaded {len(features)} samples")
            return features, labels
            
        except Exception as e:
            print(f"❌ Failed to load training data: {e}")
            return None, None
    
    def train_model(self, model, X_train, y_train, X_test, y_test, model_name, epochs=200):
        """训练模型"""
        print(f"🤖 Training {model_name}...")
        
        # 转换为张量
        X_train_tensor = torch.FloatTensor(X_train).to(self.device)
        y_train_tensor = torch.FloatTensor(y_train).to(self.device)
        X_test_tensor = torch.FloatTensor(X_test).to(self.device)
        y_test_tensor = torch.FloatTensor(y_test).to(self.device)
        
        # 优化器和损失函数
        optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=0.01)
        criterion = nn.MSELoss()
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=15, factor=0.5)
        
        # 训练历史
        history = {
            'train_loss': [],
            'test_loss': [],
            'test_r2': []
        }
        
        best_r2 = -float('inf')
        patience_counter = 0
        patience = 25
        
        for epoch in range(epochs):
            # 训练阶段
            model.train()
            train_loss = 0
            
            # 批量训练
            batch_size = 32
            for i in range(0, len(X_train_tensor), batch_size):
                batch_X = X_train_tensor[i:i+batch_size]
                batch_y = y_train_tensor[i:i+batch_size]
                
                optimizer.zero_grad()
                outputs = model(batch_X)
                loss = criterion(outputs, batch_y)
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
            
            # 测试阶段
            model.eval()
            with torch.no_grad():
                test_outputs = model(X_test_tensor)
                test_loss = criterion(test_outputs, y_test_tensor).item()
                test_r2 = r2_score(y_test_tensor.cpu().numpy(), test_outputs.cpu().numpy())
            
            # 记录历史
            history['train_loss'].append(train_loss / (len(X_train_tensor) // batch_size))
            history['test_loss'].append(test_loss)
            history['test_r2'].append(test_r2)
            
            scheduler.step(test_loss)
            
            # 早停检查
            if test_r2 > best_r2:
                best_r2 = test_r2
                patience_counter = 0
                # 保存最佳模型
                best_model_state = model.state_dict().copy()
            else:
                patience_counter += 1
            
            # 打印进度
            if (epoch + 1) % 50 == 0:
                print(f"  Epoch {epoch+1}/{epochs}: Test R² = {test_r2:.4f}, Best = {best_r2:.4f}")
            
            # 早停
            if patience_counter >= patience:
                print(f"  🛑 Early stopping at epoch {epoch+1}")
                break
        
        # 加载最佳模型
        model.load_state_dict(best_model_state)
        
        # 最终评估
        model.eval()
        with torch.no_grad():
            final_predictions = model(X_test_tensor).cpu().numpy()
            final_true = y_test_tensor.cpu().numpy()
        
        # 计算最终指标
        final_metrics = {
            'r2_score': float(r2_score(final_true, final_predictions)),
            'rmse': float(np.sqrt(mean_squared_error(final_true, final_predictions))),
            'mae': float(mean_absolute_error(final_true, final_predictions)),
            'mape': float(np.mean(np.abs((final_true - final_predictions) / (final_true + 1e-8))) * 100),
            'correlation': float(np.corrcoef(final_true, final_predictions)[0, 1]),
            'best_epoch': epoch + 1 - patience_counter,
            'final_epoch': epoch + 1
        }
        
        print(f"  ✅ {model_name} completed: R² = {final_metrics['r2_score']:.4f}")
        
        return final_metrics
    
    def run_ablation_study(self):
        """运行消融试验"""
        print("🔬 Starting Ablation Study")
        print("🎯 Testing the contribution of each module")
        print("=" * 70)
        
        # 加载数据
        features, labels = self.load_data()
        if features is None:
            return
        
        # 数据分割（使用固定随机种子确保一致性）
        X_train, X_test, y_train, y_test = train_test_split(
            features, labels, test_size=0.2, random_state=42
        )
        
        print(f"📊 Data split: Train={len(X_train)}, Test={len(X_test)}")
        
        # 消融试验配置
        ablation_configs = [
            {
                'name': 'Baseline_FC',
                'model_class': BaselineModel,
                'description': '基线模型（全连接网络）',
                'modules': []
            },
            {
                'name': 'Baseline_FC_Dropout',
                'model_class': BaselineWithDropout,
                'description': '基线模型 + Dropout',
                'modules': ['Dropout']
            },
            {
                'name': 'CNN_No_Norm',
                'model_class': CNNWithoutNorm,
                'description': 'CNN架构（无归一化）',
                'modules': ['CNN', 'Dropout']
            },
            {
                'name': 'CNN_Input_Norm',
                'model_class': CNNWithInputNorm,
                'description': 'CNN + 输入归一化',
                'modules': ['CNN', 'Dropout', 'Input_BatchNorm']
            },
            {
                'name': 'CNN_Conv_Norm',
                'model_class': CNNWithConvNorm,
                'description': 'CNN + 输入归一化 + 卷积层归一化',
                'modules': ['CNN', 'Dropout', 'Input_BatchNorm', 'Conv_BatchNorm']
            },
            {
                'name': 'Full_Model',
                'model_class': FullModel,
                'description': '完整模型（所有模块）',
                'modules': ['CNN', 'Dropout', 'Input_BatchNorm', 'Conv_BatchNorm', 'FC_BatchNorm']
            }
        ]
        
        # 运行所有消融试验
        for i, config in enumerate(ablation_configs, 1):
            print(f"\n🧪 Ablation {i}/6: {config['description']}")
            print(f"📋 Modules: {config['modules'] if config['modules'] else ['None']}")
            print("-" * 50)
            
            # 创建模型
            model = config['model_class'](64).to(self.device)
            
            # 训练模型
            metrics = self.train_model(
                model, X_train, y_train, X_test, y_test, config['name']
            )
            
            # 存储结果
            self.results[config['name']] = {
                'description': config['description'],
                'modules': config['modules'],
                'metrics': metrics
            }
        
        # 分析和保存结果
        self.analyze_ablation_results()
        self.save_ablation_results()
        self.plot_ablation_results()
    
    def analyze_ablation_results(self):
        """分析消融试验结果"""
        print("\n" + "=" * 80)
        print("📊 ABLATION STUDY RESULTS ANALYSIS")
        print("=" * 80)
        
        # 创建结果表格
        results_table = []
        for name, result in self.results.items():
            metrics = result['metrics']
            results_table.append({
                'Model': result['description'],
                'Modules': ' + '.join(result['modules']) if result['modules'] else 'None',
                'R²': metrics['r2_score'],
                'RMSE': metrics['rmse'],
                'MAE': metrics['mae'],
                'MAPE': metrics['mape'],
                'Improvement': 0  # 将在下面计算
            })
        
        # 计算性能提升
        baseline_r2 = results_table[0]['R²']
        for result in results_table:
            result['Improvement'] = ((result['R²'] - baseline_r2) / abs(baseline_r2)) * 100
        
        # 按R²排序
        results_table.sort(key=lambda x: x['R²'], reverse=True)
        
        print(f"\n🏆 Performance Ranking:")
        print("-" * 80)
        for i, result in enumerate(results_table, 1):
            print(f"{i}. {result['Model']}")
            print(f"   Modules: {result['Modules']}")
            print(f"   R² = {result['R²']:.4f}, RMSE = {result['RMSE']:.4f}")
            print(f"   Improvement: {result['Improvement']:+.1f}% vs Baseline")
            print()
        
        # 模块贡献分析
        print(f"📈 Module Contribution Analysis:")
        print("-" * 50)
        
        # 计算每个模块的贡献
        module_contributions = {}
        
        # CNN架构的贡献
        baseline_r2 = self.results['Baseline_FC']['metrics']['r2_score']
        cnn_r2 = self.results['CNN_No_Norm']['metrics']['r2_score']
        module_contributions['CNN_Architecture'] = cnn_r2 - baseline_r2
        
        # Dropout的贡献
        dropout_r2 = self.results['Baseline_FC_Dropout']['metrics']['r2_score']
        module_contributions['Dropout'] = dropout_r2 - baseline_r2
        
        # 输入归一化的贡献
        input_norm_r2 = self.results['CNN_Input_Norm']['metrics']['r2_score']
        module_contributions['Input_BatchNorm'] = input_norm_r2 - cnn_r2
        
        # 卷积归一化的贡献
        conv_norm_r2 = self.results['CNN_Conv_Norm']['metrics']['r2_score']
        module_contributions['Conv_BatchNorm'] = conv_norm_r2 - input_norm_r2
        
        # 全连接归一化的贡献
        full_r2 = self.results['Full_Model']['metrics']['r2_score']
        module_contributions['FC_BatchNorm'] = full_r2 - conv_norm_r2
        
        # 打印模块贡献
        for module, contribution in module_contributions.items():
            print(f"  {module}: {contribution:+.4f} R² improvement")
        
        # 最重要的模块
        best_module = max(module_contributions.items(), key=lambda x: x[1])
        print(f"\n🌟 Most Important Module: {best_module[0]} (+{best_module[1]:.4f} R²)")
    
    def save_ablation_results(self):
        """保存消融试验结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存详细结果
        results_filename = f"ablation_study_results_{timestamp}.json"
        with open(results_filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Results saved: {results_filename}")
        
        # 保存CSV摘要
        summary_data = []
        for name, result in self.results.items():
            metrics = result['metrics']
            summary_data.append({
                'Model_Name': name,
                'Description': result['description'],
                'Modules': ' + '.join(result['modules']) if result['modules'] else 'None',
                'R2_Score': metrics['r2_score'],
                'RMSE': metrics['rmse'],
                'MAE': metrics['mae'],
                'MAPE_Percent': metrics['mape'],
                'Correlation': metrics['correlation'],
                'Best_Epoch': metrics['best_epoch'],
                'Final_Epoch': metrics['final_epoch']
            })
        
        df = pd.DataFrame(summary_data)
        csv_filename = f"ablation_study_summary_{timestamp}.csv"
        df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
        
        print(f"📊 Summary CSV saved: {csv_filename}")
    
    def plot_ablation_results(self):
        """绘制消融试验结果"""
        try:
            # 提取数据
            names = []
            r2_scores = []
            
            # 按照试验顺序排列
            order = ['Baseline_FC', 'Baseline_FC_Dropout', 'CNN_No_Norm', 
                    'CNN_Input_Norm', 'CNN_Conv_Norm', 'Full_Model']
            
            for name in order:
                if name in self.results:
                    names.append(self.results[name]['description'])
                    r2_scores.append(self.results[name]['metrics']['r2_score'])
            
            # 创建图表
            plt.figure(figsize=(14, 8))
            
            # 柱状图
            bars = plt.bar(range(len(names)), r2_scores, alpha=0.7)
            
            # 颜色渐变
            colors = plt.cm.viridis(np.linspace(0, 1, len(names)))
            for bar, color in zip(bars, colors):
                bar.set_color(color)
            
            plt.xlabel('Model Configuration')
            plt.ylabel('R² Score')
            plt.title('Ablation Study: Performance with Progressive Module Addition')
            plt.xticks(range(len(names)), [name.replace(' ', '\n') for name in names], 
                      rotation=45, ha='right')
            plt.grid(True, alpha=0.3, axis='y')
            
            # 添加数值标签
            for i, score in enumerate(r2_scores):
                plt.text(i, score + 0.01, f'{score:.3f}', ha='center', va='bottom', fontweight='bold')
            
            # 添加改进线
            if len(r2_scores) > 1:
                plt.plot(range(len(r2_scores)), r2_scores, 'ro-', alpha=0.7, linewidth=2, markersize=6)
            
            plt.tight_layout()
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            plot_filename = f"ablation_study_plot_{timestamp}.png"
            plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
            plt.show()
            
            print(f"📊 Ablation study plot saved: {plot_filename}")
            
        except Exception as e:
            print(f"⚠️ Plotting failed: {e}")


def main():
    """主函数"""
    print("🔬 CNN Model Ablation Study")
    print("🎯 Testing the contribution of each module")
    print("=" * 60)
    
    try:
        # 创建并运行消融试验
        study = AblationStudy()
        study.run_ablation_study()
        
        print(f"\n✅ Ablation study completed successfully!")
        print(f"🎉 Check the generated files for detailed analysis!")
        
    except Exception as e:
        print(f"❌ Ablation study failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
