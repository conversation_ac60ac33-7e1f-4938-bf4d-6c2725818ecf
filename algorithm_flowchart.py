import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from matplotlib.patches import FancyBboxPatch, Rectangle, Circle, Arrow
import numpy as np
from datetime import datetime

# 设置matplotlib支持中文
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class AlgorithmFlowchart:
    """智能气吹控制系统算法流程图生成器"""
    
    def __init__(self):
        self.fig, self.ax = plt.subplots(1, 1, figsize=(16, 20))
        self.ax.set_xlim(0, 10)
        self.ax.set_ylim(0, 25)
        self.ax.axis('off')
        
        # 定义颜色方案
        self.colors = {
            'input': '#E3F2FD',      # 浅蓝色 - 输入
            'process': '#E8F5E8',    # 浅绿色 - 处理
            'decision': '#FFF3E0',   # 浅橙色 - 判断
            'output': '#F3E5F5',     # 浅紫色 - 输出
            'model': '#FFEBEE',      # 浅红色 - 模型
            'control': '#E0F2F1'     # 浅青色 - 控制
        }
        
        print("📊 算法流程图生成器已初始化")
    
    def draw_box(self, x, y, width, height, text, color, text_size=10):
        """绘制流程框"""
        box = FancyBboxPatch(
            (x - width/2, y - height/2), width, height,
            boxstyle="round,pad=0.1",
            facecolor=color,
            edgecolor='black',
            linewidth=1.5
        )
        self.ax.add_patch(box)
        
        # 添加文本
        self.ax.text(x, y, text, ha='center', va='center', 
                    fontsize=text_size, fontweight='bold', wrap=True)
    
    def draw_diamond(self, x, y, width, height, text, color, text_size=9):
        """绘制菱形判断框"""
        diamond = mpatches.RegularPolygon(
            (x, y), 4, radius=width/2,
            orientation=np.pi/4,
            facecolor=color,
            edgecolor='black',
            linewidth=1.5
        )
        self.ax.add_patch(diamond)
        
        # 添加文本
        self.ax.text(x, y, text, ha='center', va='center', 
                    fontsize=text_size, fontweight='bold')
    
    def draw_arrow(self, x1, y1, x2, y2, text='', offset=0.2):
        """绘制箭头"""
        self.ax.annotate('', xy=(x2, y2), xytext=(x1, y1),
                        arrowprops=dict(arrowstyle='->', lw=2, color='black'))
        
        # 添加箭头标签
        if text:
            mid_x, mid_y = (x1 + x2) / 2 + offset, (y1 + y2) / 2
            self.ax.text(mid_x, mid_y, text, ha='center', va='center',
                        fontsize=8, bbox=dict(boxstyle="round,pad=0.3", 
                        facecolor='white', alpha=0.8))
    
    def create_main_flowchart(self):
        """创建主要算法流程图"""
        # 标题
        self.ax.text(5, 24, '智能草莓气吹控制系统算法流程图', 
                    ha='center', va='center', fontsize=16, fontweight='bold')
        
        # 1. 输入阶段
        self.draw_box(5, 22.5, 3, 0.8, '视频输入\n(草莓采摘场景)', 
                     self.colors['input'], 11)
        
        # 2. YOLOv11分割
        self.draw_box(5, 21, 3.5, 0.8, 'YOLOv11-seg目标检测\n与实例分割', 
                     self.colors['model'], 11)
        self.draw_arrow(5, 22.1, 5, 21.4)
        
        # 3. 检测结果判断
        self.draw_diamond(5, 19.5, 2, 1, '检测到\n草莓?', 
                         self.colors['decision'], 10)
        self.draw_arrow(5, 20.6, 5, 20)
        
        # 3a. 无检测结果分支
        self.draw_box(8, 19.5, 2, 0.6, '跳过当前帧', 
                     self.colors['process'], 9)
        self.draw_arrow(6, 19.5, 7, 19.5, '否')
        
        # 4. DeepSORT跟踪
        self.draw_box(5, 18, 3, 0.8, 'DeepSORT多目标跟踪\n分配草莓ID', 
                     self.colors['model'], 10)
        self.draw_arrow(5, 19, 5, 18.4, '是')
        
        # 5. 掩膜处理
        self.draw_box(5, 16.5, 3.5, 0.8, '提取草莓掩膜和\n遮挡掩膜', 
                     self.colors['process'], 10)
        self.draw_arrow(5, 17.6, 5, 17)
        
        # 6. 特征提取
        self.draw_box(5, 15, 4, 0.8, '64维特征提取\n(颜色、纹理、形状、梯度)', 
                     self.colors['process'], 10)
        self.draw_arrow(5, 16.1, 5, 15.4)
        
        # 7. CNN回归预测
        self.draw_box(5, 13.5, 4, 0.8, 'CNN回归器预测\n残留遮挡率', 
                     self.colors['model'], 11)
        self.draw_arrow(5, 14.6, 5, 13.9)
        
        # 8. 气吹参数计算
        self.draw_box(5, 12, 4, 0.8, '计算气吹参数\n(强度、持续时间、角度)', 
                     self.colors['control'], 10)
        self.draw_arrow(5, 13.1, 5, 12.4)
        
        # 9. 气吹控制执行
        self.draw_box(5, 10.5, 3.5, 0.8, '执行智能气吹控制', 
                     self.colors['control'], 11)
        self.draw_arrow(5, 11.6, 5, 10.9)
        
        # 10. 结果输出
        self.draw_box(5, 9, 3, 0.8, '输出处理结果\n和控制参数', 
                     self.colors['output'], 10)
        self.draw_arrow(5, 10.1, 5, 9.4)
        
        # 11. 循环判断
        self.draw_diamond(5, 7.5, 2, 1, '还有\n下一帧?', 
                         self.colors['decision'], 10)
        self.draw_arrow(5, 8.6, 5, 8)
        
        # 循环箭头
        self.draw_arrow(6, 7.5, 8.5, 7.5, '是')
        self.draw_arrow(8.5, 7.5, 8.5, 22.5)
        self.draw_arrow(8.5, 22.5, 6.5, 22.5)
        
        # 结束
        self.draw_box(5, 6, 2, 0.6, '处理完成', 
                     self.colors['output'], 10)
        self.draw_arrow(5, 7, 5, 6.3, '否')
        
        # 添加侧边详细说明
        self.add_detailed_annotations()
        
        # 添加图例
        self.add_legend()
    
    def add_detailed_annotations(self):
        """添加详细说明"""
        # 右侧详细说明
        annotations = [
            (0.5, 21, "输入处理", "• 读取视频帧\n• 图像预处理\n• 尺寸调整"),
            (0.5, 18.5, "目标检测", "• 检测草莓位置\n• 分割草莓轮廓\n• 识别遮挡区域"),
            (0.5, 16, "特征工程", "• 颜色特征(HSV)\n• 纹理特征(LBP)\n• 形状特征(轮廓)\n• 梯度特征"),
            (0.5, 13.5, "CNN预测", "• R² = 0.9175\n• RMSE = 0.0236\n• MAE = 0.0178"),
            (0.5, 11, "智能控制", "• 气吹强度优化\n• 持续时间计算\n• 舵机角度调节")
        ]
        
        for x, y, title, content in annotations:
            # 标题
            self.ax.text(x, y + 0.3, title, fontsize=10, fontweight='bold', color='blue')
            # 内容
            self.ax.text(x, y - 0.2, content, fontsize=8, va='top')
    
    def add_legend(self):
        """添加图例"""
        legend_elements = [
            mpatches.Patch(color=self.colors['input'], label='输入/输出'),
            mpatches.Patch(color=self.colors['model'], label='AI模型'),
            mpatches.Patch(color=self.colors['process'], label='数据处理'),
            mpatches.Patch(color=self.colors['decision'], label='判断决策'),
            mpatches.Patch(color=self.colors['control'], label='控制执行')
        ]
        
        self.ax.legend(handles=legend_elements, loc='upper right', 
                      bbox_to_anchor=(0.98, 0.98), fontsize=10)
    
    def create_cnn_detail_flowchart(self):
        """创建CNN模型详细流程图"""
        fig2, ax2 = plt.subplots(1, 1, figsize=(14, 10))
        ax2.set_xlim(0, 10)
        ax2.set_ylim(0, 12)
        ax2.axis('off')
        
        # 标题
        ax2.text(5, 11.5, 'CNN回归器详细流程', ha='center', va='center', 
                fontsize=16, fontweight='bold')
        
        # CNN流程
        steps = [
            (5, 10.5, "64维特征输入", self.colors['input']),
            (5, 9.5, "BatchNorm归一化", self.colors['process']),
            (5, 8.5, "1D卷积层1 (32通道)", self.colors['model']),
            (5, 7.5, "1D卷积层2 (64通道)", self.colors['model']),
            (5, 6.5, "1D卷积层3 (32通道)", self.colors['model']),
            (5, 5.5, "全局平均池化", self.colors['process']),
            (5, 4.5, "全连接层1 (64→32)", self.colors['model']),
            (5, 3.5, "全连接层2 (32→1)", self.colors['model']),
            (5, 2.5, "Sigmoid激活", self.colors['process']),
            (5, 1.5, "残留遮挡率输出", self.colors['output'])
        ]
        
        for i, (x, y, text, color) in enumerate(steps):
            box = FancyBboxPatch(
                (x - 1.5, y - 0.3), 3, 0.6,
                boxstyle="round,pad=0.1",
                facecolor=color,
                edgecolor='black',
                linewidth=1.5
            )
            ax2.add_patch(box)
            ax2.text(x, y, text, ha='center', va='center', fontsize=10, fontweight='bold')
            
            if i < len(steps) - 1:
                ax2.annotate('', xy=(x, y - 0.4), xytext=(x, y - 0.6),
                           arrowprops=dict(arrowstyle='->', lw=2, color='black'))
        
        # 侧边说明
        side_notes = [
            (8.5, 9, "特征标准化\n提高收敛速度"),
            (8.5, 7.5, "1D卷积提取\n时序特征"),
            (8.5, 5.5, "降维处理\n减少参数"),
            (8.5, 3, "非线性映射\n复杂关系建模"),
            (8.5, 1.5, "输出范围[0,1]\n表示遮挡比例")
        ]
        
        for x, y, text in side_notes:
            ax2.text(x, y, text, fontsize=9, ha='center', va='center',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor='lightyellow', alpha=0.8))
        
        return fig2
    
    def create_feature_extraction_flowchart(self):
        """创建特征提取详细流程图"""
        fig3, ax3 = plt.subplots(1, 1, figsize=(16, 8))
        ax3.set_xlim(0, 16)
        ax3.set_ylim(0, 8)
        ax3.axis('off')
        
        # 标题
        ax3.text(8, 7.5, '64维特征提取详细流程', ha='center', va='center', 
                fontsize=16, fontweight='bold')
        
        # 四个特征提取分支
        branches = [
            # 颜色特征
            {
                'title': '颜色特征 (16维)',
                'x': 2,
                'steps': [
                    'RGB→HSV转换',
                    'HSV直方图',
                    '颜色矩统计',
                    '主色调提取'
                ]
            },
            # 纹理特征
            {
                'title': '纹理特征 (16维)',
                'x': 6,
                'steps': [
                    'LBP纹理描述',
                    'GLCM矩阵',
                    '纹理对比度',
                    '纹理均匀性'
                ]
            },
            # 形状特征
            {
                'title': '形状特征 (16维)',
                'x': 10,
                'steps': [
                    '轮廓提取',
                    '几何矩计算',
                    '形状描述子',
                    '边界特征'
                ]
            },
            # 梯度特征
            {
                'title': '梯度特征 (16维)',
                'x': 14,
                'steps': [
                    'Sobel算子',
                    '梯度幅值',
                    '梯度方向',
                    '边缘密度'
                ]
            }
        ]
        
        for branch in branches:
            x = branch['x']
            # 标题
            ax3.text(x, 6.5, branch['title'], ha='center', va='center', 
                    fontsize=12, fontweight='bold', color='blue')
            
            # 步骤
            for i, step in enumerate(branch['steps']):
                y = 5.5 - i * 0.8
                box = FancyBboxPatch(
                    (x - 0.8, y - 0.25), 1.6, 0.5,
                    boxstyle="round,pad=0.05",
                    facecolor=self.colors['process'],
                    edgecolor='black',
                    linewidth=1
                )
                ax3.add_patch(box)
                ax3.text(x, y, step, ha='center', va='center', fontsize=9)
                
                if i < len(branch['steps']) - 1:
                    ax3.annotate('', xy=(x, y - 0.35), xytext=(x, y - 0.45),
                               arrowprops=dict(arrowstyle='->', lw=1.5, color='black'))
        
        # 特征融合
        ax3.text(8, 1.5, '特征向量融合', ha='center', va='center', 
                fontsize=14, fontweight='bold')
        
        # 融合箭头
        for x in [2, 6, 10, 14]:
            ax3.annotate('', xy=(8, 2), xytext=(x, 2.3),
                       arrowprops=dict(arrowstyle='->', lw=2, color='red'))
        
        # 最终输出
        final_box = FancyBboxPatch(
            (6, 0.5), 4, 0.8,
            boxstyle="round,pad=0.1",
            facecolor=self.colors['output'],
            edgecolor='black',
            linewidth=2
        )
        ax3.add_patch(final_box)
        ax3.text(8, 0.9, '64维特征向量\n输入CNN回归器', ha='center', va='center', 
                fontsize=12, fontweight='bold')
        
        return fig3
    
    def save_all_flowcharts(self):
        """保存所有流程图"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 主流程图
        self.create_main_flowchart()
        main_filename = f"智能气吹系统主流程图_{timestamp}.png"
        self.fig.savefig(main_filename, dpi=300, bbox_inches='tight')
        
        # CNN详细流程图
        fig2 = self.create_cnn_detail_flowchart()
        cnn_filename = f"CNN回归器详细流程图_{timestamp}.png"
        fig2.savefig(cnn_filename, dpi=300, bbox_inches='tight')
        
        # 特征提取流程图
        fig3 = self.create_feature_extraction_flowchart()
        feature_filename = f"特征提取详细流程图_{timestamp}.png"
        fig3.savefig(feature_filename, dpi=300, bbox_inches='tight')
        
        print(f"📊 流程图已保存:")
        print(f"  🎯 主流程图: {main_filename}")
        print(f"  🤖 CNN流程图: {cnn_filename}")
        print(f"  🔍 特征提取图: {feature_filename}")
        
        # 显示图表
        plt.show()
        
        return main_filename, cnn_filename, feature_filename


def main():
    """主函数"""
    print("📊 智能气吹控制系统算法流程图生成器")
    print("🎯 生成完整的算法流程图")
    print("=" * 50)
    
    try:
        # 创建流程图生成器
        flowchart = AlgorithmFlowchart()
        
        # 生成并保存所有流程图
        main_file, cnn_file, feature_file = flowchart.save_all_flowcharts()
        
        print(f"\n✅ 算法流程图生成完成!")
        print(f"🎉 包含主流程、CNN详细流程、特征提取流程!")
        
    except Exception as e:
        print(f"❌ 流程图生成失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
