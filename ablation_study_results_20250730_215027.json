{"Baseline_FC": {"description": "基线模型（全连接网络）", "modules": [], "metrics": {"r2_score": 0.9459450449186906, "rmse": 0.018933145329356194, "mae": 0.01413235068321228, "mape": 13.601560890674591, "correlation": 0.9726403996388041, "best_epoch": 82, "final_epoch": 107}}, "Baseline_FC_Dropout": {"description": "基线模型 + Dropout", "modules": ["Dropout"], "metrics": {"r2_score": 0.9248949587604415, "rmse": 0.0223171915858984, "mae": 0.017222007736563683, "mape": 16.73332452774048, "correlation": 0.9679721883776735, "best_epoch": 105, "final_epoch": 130}}, "CNN_No_Norm": {"description": "CNN架构（无归一化）", "modules": ["CNN", "Dropout"], "metrics": {"r2_score": 0.5823928599127509, "rmse": 0.052624620497226715, "mae": 0.0463235005736351, "mape": 67.99381375312805, "correlation": 0.787572365070626, "best_epoch": 40, "final_epoch": 65}}, "CNN_Input_Norm": {"description": "CNN + 输入归一化", "modules": ["CNN", "Dropout", "Input_BatchNorm"], "metrics": {"r2_score": 0.84974365570222, "rmse": 0.031566135585308075, "mae": 0.02357448637485504, "mape": 28.867334127426147, "correlation": 0.9373280647565502, "best_epoch": 190, "final_epoch": 200}}, "CNN_Conv_Norm": {"description": "CNN + 输入归一化 + 卷积层归一化", "modules": ["CNN", "Dropout", "Input_BatchNorm", "Conv_BatchNorm"], "metrics": {"r2_score": 0.9224951893611292, "rmse": 0.02267093025147915, "mae": 0.017387116327881813, "mape": 20.305687189102173, "correlation": 0.9661547458485906, "best_epoch": 156, "final_epoch": 181}}, "Full_Model": {"description": "完整模型（所有模块）", "modules": ["CNN", "Dropout", "Input_BatchNorm", "Conv_BatchNorm", "FC_BatchNorm"], "metrics": {"r2_score": 0.9057344857688767, "rmse": 0.025002384558320045, "mae": 0.018234753981232643, "mape": 19.716544449329376, "correlation": 0.9560667120058896, "best_epoch": 107, "final_epoch": 132}}}