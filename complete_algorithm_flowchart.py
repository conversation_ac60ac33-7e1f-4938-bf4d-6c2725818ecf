import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from matplotlib.patches import FancyBboxPatch, Rectangle, Circle, Arrow
import numpy as np
from datetime import datetime

# 设置matplotlib支持中文
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class CompleteAlgorithmFlowchart:
    """完整的智能气吹控制系统算法流程图生成器（包含螺旋搜索和区域生长）"""
    
    def __init__(self):
        self.fig, self.ax = plt.subplots(1, 1, figsize=(18, 24))
        self.ax.set_xlim(0, 12)
        self.ax.set_ylim(0, 30)
        self.ax.axis('off')
        
        # 定义颜色方案
        self.colors = {
            'input': '#E3F2FD',      # 浅蓝色 - 输入
            'process': '#E8F5E8',    # 浅绿色 - 处理
            'decision': '#FFF3E0',   # 浅橙色 - 判断
            'output': '#F3E5F5',     # 浅紫色 - 输出
            'model': '#FFEBEE',      # 浅红色 - 模型
            'control': '#E0F2F1',    # 浅青色 - 控制
            'algorithm': '#FFF8E1',  # 浅黄色 - 算法
            'optimization': '#E8EAF6' # 浅靛色 - 优化
        }
        
        print("📊 完整算法流程图生成器已初始化")
    
    def draw_box(self, x, y, width, height, text, color, text_size=10):
        """绘制流程框"""
        box = FancyBboxPatch(
            (x - width/2, y - height/2), width, height,
            boxstyle="round,pad=0.1",
            facecolor=color,
            edgecolor='black',
            linewidth=1.5
        )
        self.ax.add_patch(box)
        
        # 添加文本
        self.ax.text(x, y, text, ha='center', va='center', 
                    fontsize=text_size, fontweight='bold', wrap=True)
    
    def draw_diamond(self, x, y, width, height, text, color, text_size=9):
        """绘制菱形判断框"""
        diamond = mpatches.RegularPolygon(
            (x, y), 4, radius=width/2,
            orientation=np.pi/4,
            facecolor=color,
            edgecolor='black',
            linewidth=1.5
        )
        self.ax.add_patch(diamond)
        
        # 添加文本
        self.ax.text(x, y, text, ha='center', va='center', 
                    fontsize=text_size, fontweight='bold')
    
    def draw_arrow(self, x1, y1, x2, y2, text='', offset=0.2):
        """绘制箭头"""
        self.ax.annotate('', xy=(x2, y2), xytext=(x1, y1),
                        arrowprops=dict(arrowstyle='->', lw=2, color='black'))
        
        # 添加箭头标签
        if text:
            mid_x, mid_y = (x1 + x2) / 2 + offset, (y1 + y2) / 2
            self.ax.text(mid_x, mid_y, text, ha='center', va='center',
                        fontsize=8, bbox=dict(boxstyle="round,pad=0.3", 
                        facecolor='white', alpha=0.8))
    
    def create_complete_flowchart(self):
        """创建完整的算法流程图"""
        # 标题
        self.ax.text(6, 29, '完整智能草莓气吹控制系统算法流程图', 
                    ha='center', va='center', fontsize=18, fontweight='bold')
        self.ax.text(6, 28.3, '(包含螺旋搜索算法和区域生长算法)', 
                    ha='center', va='center', fontsize=14, fontweight='bold', color='red')
        
        # 1. 输入阶段
        self.draw_box(6, 27, 3, 0.8, '视频输入\n(草莓采摘场景)', 
                     self.colors['input'], 11)
        
        # 2. YOLOv11分割
        self.draw_box(6, 25.5, 3.5, 0.8, 'YOLOv11-seg目标检测\n与实例分割', 
                     self.colors['model'], 11)
        self.draw_arrow(6, 26.6, 6, 25.9)
        
        # 3. 检测结果判断
        self.draw_diamond(6, 24, 2, 1, '检测到\n草莓?', 
                         self.colors['decision'], 10)
        self.draw_arrow(6, 25.1, 6, 24.5)
        
        # 3a. 无检测结果分支
        self.draw_box(9.5, 24, 2, 0.6, '跳过当前帧', 
                     self.colors['process'], 9)
        self.draw_arrow(7, 24, 8.5, 24, '否')
        
        # 4. 螺旋搜索算法
        self.draw_box(6, 22.5, 4, 0.8, '螺旋搜索算法\n精确定位草莓中心', 
                     self.colors['algorithm'], 11)
        self.draw_arrow(6, 23.5, 6, 22.9, '是')
        
        # 5. 区域生长算法
        self.draw_box(6, 21, 4, 0.8, '区域生长算法\n分割草莓完整区域', 
                     self.colors['algorithm'], 11)
        self.draw_arrow(6, 22.1, 6, 21.4)
        
        # 6. DeepSORT跟踪
        self.draw_box(6, 19.5, 3.5, 0.8, 'DeepSORT多目标跟踪\n分配草莓ID', 
                     self.colors['model'], 10)
        self.draw_arrow(6, 20.6, 6, 19.9)
        
        # 7. 掩膜优化处理
        self.draw_box(6, 18, 4, 0.8, '掩膜优化处理\n提取草莓和遮挡掩膜', 
                     self.colors['process'], 10)
        self.draw_arrow(6, 19.1, 6, 18.4)
        
        # 8. 64维特征提取
        self.draw_box(6, 16.5, 4.5, 0.8, '64维特征提取\n(颜色、纹理、形状、梯度)', 
                     self.colors['process'], 10)
        self.draw_arrow(6, 17.6, 6, 16.9)
        
        # 9. CNN回归预测
        self.draw_box(6, 15, 4, 0.8, 'CNN回归器预测\n残留遮挡率', 
                     self.colors['model'], 11)
        self.draw_arrow(6, 16.1, 6, 15.4)
        
        # 10. 气吹参数优化
        self.draw_box(6, 13.5, 4.5, 0.8, '气吹参数优化算法\n(强度、持续时间、角度)', 
                     self.colors['optimization'], 10)
        self.draw_arrow(6, 14.6, 6, 13.9)
        
        # 11. 智能气吹控制
        self.draw_box(6, 12, 4, 0.8, '执行智能气吹控制\n舵机精确定位', 
                     self.colors['control'], 11)
        self.draw_arrow(6, 13.1, 6, 12.4)
        
        # 12. 效果评估
        self.draw_diamond(6, 10.5, 2.5, 1, '气吹效果\n满足要求?', 
                         self.colors['decision'], 10)
        self.draw_arrow(6, 11.6, 6, 11)
        
        # 12a. 重新气吹分支
        self.draw_box(9.5, 10.5, 2.5, 0.6, '调整参数\n重新气吹', 
                     self.colors['control'], 9)
        self.draw_arrow(7.2, 10.5, 8.2, 10.5, '否')
        self.draw_arrow(9.5, 10.8, 9.5, 13.5)
        self.draw_arrow(9.5, 13.5, 8.2, 13.5)
        
        # 13. 结果输出
        self.draw_box(6, 9, 3.5, 0.8, '输出处理结果\n和控制参数', 
                     self.colors['output'], 10)
        self.draw_arrow(6, 10, 6, 9.4, '是')
        
        # 14. 循环判断
        self.draw_diamond(6, 7.5, 2, 1, '还有\n下一帧?', 
                         self.colors['decision'], 10)
        self.draw_arrow(6, 8.6, 6, 8)
        
        # 循环箭头
        self.draw_arrow(7, 7.5, 10.5, 7.5, '是')
        self.draw_arrow(10.5, 7.5, 10.5, 27)
        self.draw_arrow(10.5, 27, 7.5, 27)
        
        # 结束
        self.draw_box(6, 6, 2.5, 0.6, '处理完成', 
                     self.colors['output'], 10)
        self.draw_arrow(6, 7, 6, 6.3, '否')
        
        # 添加详细说明
        self.add_algorithm_details()
        
        # 添加图例
        self.add_complete_legend()
    
    def add_algorithm_details(self):
        """添加算法详细说明"""
        # 左侧算法详细说明
        left_annotations = [
            (1, 22.5, "螺旋搜索算法", "• 从检测框中心开始\n• 螺旋式扩展搜索\n• 寻找最佳草莓中心\n• 提高定位精度"),
            (1, 21, "区域生长算法", "• 以螺旋搜索结果为种子\n• 基于颜色相似性生长\n• 分割完整草莓区域\n• 优化分割边界"),
            (1, 16.5, "特征工程", "• 颜色特征(HSV)\n• 纹理特征(LBP)\n• 形状特征(轮廓)\n• 梯度特征(Sobel)"),
            (1, 13.5, "参数优化", "• 基于CNN预测结果\n• 自适应参数调节\n• 多目标优化算法\n• 实时参数更新")
        ]
        
        # 右侧性能指标说明
        right_annotations = [
            (11, 25.5, "检测性能", "• YOLOv11-seg\n• 检测精度: 89.2%\n• 分割IoU: 0.847\n• 处理速度: 13.7 FPS"),
            (11, 19.5, "跟踪性能", "• DeepSORT算法\n• 跟踪稳定性: 84.7%\n• ID切换率: <5%\n• 多目标处理"),
            (11, 15, "CNN性能", "• R² = 0.9175\n• RMSE = 0.0236\n• MAE = 0.0178\n• 预测精度: 91.75%"),
            (11, 12, "控制性能", "• 气吹精度: ±2°\n• 响应时间: <100ms\n• 成功率: >95%\n• 能耗优化: 30%")
        ]
        
        for x, y, title, content in left_annotations + right_annotations:
            # 标题
            self.ax.text(x, y + 0.4, title, fontsize=10, fontweight='bold', color='blue')
            # 内容
            self.ax.text(x, y - 0.2, content, fontsize=8, va='top')
    
    def add_complete_legend(self):
        """添加完整图例"""
        legend_elements = [
            mpatches.Patch(color=self.colors['input'], label='输入/输出'),
            mpatches.Patch(color=self.colors['model'], label='AI模型'),
            mpatches.Patch(color=self.colors['algorithm'], label='核心算法'),
            mpatches.Patch(color=self.colors['process'], label='数据处理'),
            mpatches.Patch(color=self.colors['decision'], label='判断决策'),
            mpatches.Patch(color=self.colors['optimization'], label='参数优化'),
            mpatches.Patch(color=self.colors['control'], label='控制执行')
        ]
        
        self.ax.legend(handles=legend_elements, loc='upper right', 
                      bbox_to_anchor=(0.98, 0.98), fontsize=10)
    
    def create_spiral_search_detail(self):
        """创建螺旋搜索算法详细流程图"""
        fig2, ax2 = plt.subplots(1, 1, figsize=(14, 10))
        ax2.set_xlim(0, 10)
        ax2.set_ylim(0, 12)
        ax2.axis('off')
        
        # 标题
        ax2.text(5, 11.5, '螺旋搜索算法详细流程', ha='center', va='center', 
                fontsize=16, fontweight='bold')
        
        # 螺旋搜索流程
        steps = [
            (5, 10.5, "输入检测框坐标", self.colors['input']),
            (5, 9.5, "计算检测框中心点", self.colors['process']),
            (5, 8.5, "初始化螺旋参数", self.colors['algorithm']),
            (5, 7.5, "螺旋式搜索扩展", self.colors['algorithm']),
            (5, 6.5, "计算像素相似度", self.colors['process']),
            (5, 5.5, "更新最佳中心点", self.colors['optimization']),
            (5, 4.5, "判断收敛条件", self.colors['decision']),
            (5, 3.5, "输出精确中心坐标", self.colors['output'])
        ]
        
        for i, (x, y, text, color) in enumerate(steps):
            if i == 6:  # 判断框
                diamond = mpatches.RegularPolygon(
                    (x, y), 4, radius=0.8,
                    orientation=np.pi/4,
                    facecolor=color,
                    edgecolor='black',
                    linewidth=1.5
                )
                ax2.add_patch(diamond)
            else:
                box = FancyBboxPatch(
                    (x - 1.5, y - 0.3), 3, 0.6,
                    boxstyle="round,pad=0.1",
                    facecolor=color,
                    edgecolor='black',
                    linewidth=1.5
                )
                ax2.add_patch(box)
            
            ax2.text(x, y, text, ha='center', va='center', fontsize=10, fontweight='bold')
            
            if i < len(steps) - 1 and i != 6:
                ax2.annotate('', xy=(x, y - 0.4), xytext=(x, y - 0.6),
                           arrowprops=dict(arrowstyle='->', lw=2, color='black'))
            elif i == 6:
                # 收敛判断的箭头
                ax2.annotate('', xy=(x, y - 0.6), xytext=(x, y - 0.4),
                           arrowprops=dict(arrowstyle='->', lw=2, color='black'))
                # 未收敛的循环箭头
                ax2.annotate('', xy=(7.5, y), xytext=(6.5, y),
                           arrowprops=dict(arrowstyle='->', lw=2, color='red'))
                ax2.text(7, y + 0.3, '未收敛', ha='center', va='center', fontsize=8, color='red')
                ax2.annotate('', xy=(7.5, 7.5), xytext=(7.5, y),
                           arrowprops=dict(arrowstyle='->', lw=2, color='red'))
                ax2.annotate('', xy=(6.5, 7.5), xytext=(7.5, 7.5),
                           arrowprops=dict(arrowstyle='->', lw=2, color='red'))
        
        # 算法参数说明
        ax2.text(8.5, 9, "算法参数:\n• 初始半径: 5像素\n• 扩展步长: 2像素\n• 最大迭代: 50次\n• 收敛阈值: 0.01", 
                fontsize=9, ha='left', va='top',
                bbox=dict(boxstyle="round,pad=0.3", facecolor='lightyellow', alpha=0.8))
        
        return fig2
    
    def create_region_growing_detail(self):
        """创建区域生长算法详细流程图"""
        fig3, ax3 = plt.subplots(1, 1, figsize=(14, 10))
        ax3.set_xlim(0, 10)
        ax3.set_ylim(0, 12)
        ax3.axis('off')
        
        # 标题
        ax3.text(5, 11.5, '区域生长算法详细流程', ha='center', va='center', 
                fontsize=16, fontweight='bold')
        
        # 区域生长流程
        steps = [
            (5, 10.5, "输入螺旋搜索中心点", self.colors['input']),
            (5, 9.5, "设置种子点和阈值", self.colors['algorithm']),
            (5, 8.5, "初始化生长队列", self.colors['process']),
            (5, 7.5, "检查邻域像素", self.colors['algorithm']),
            (5, 6.5, "计算颜色相似度", self.colors['process']),
            (5, 5.5, "判断生长条件", self.colors['decision']),
            (5, 4.5, "添加到生长区域", self.colors['algorithm']),
            (5, 3.5, "更新生长边界", self.colors['process']),
            (5, 2.5, "输出分割掩膜", self.colors['output'])
        ]
        
        for i, (x, y, text, color) in enumerate(steps):
            if i == 5:  # 判断框
                diamond = mpatches.RegularPolygon(
                    (x, y), 4, radius=0.8,
                    orientation=np.pi/4,
                    facecolor=color,
                    edgecolor='black',
                    linewidth=1.5
                )
                ax3.add_patch(diamond)
            else:
                box = FancyBboxPatch(
                    (x - 1.5, y - 0.3), 3, 0.6,
                    boxstyle="round,pad=0.1",
                    facecolor=color,
                    edgecolor='black',
                    linewidth=1.5
                )
                ax3.add_patch(box)
            
            ax3.text(x, y, text, ha='center', va='center', fontsize=10, fontweight='bold')
            
            if i < len(steps) - 1 and i not in [5, 7]:
                ax3.annotate('', xy=(x, y - 0.4), xytext=(x, y - 0.6),
                           arrowprops=dict(arrowstyle='->', lw=2, color='black'))
            elif i == 5:
                # 满足条件的箭头
                ax3.annotate('', xy=(x, y - 0.6), xytext=(x, y - 0.4),
                           arrowprops=dict(arrowstyle='->', lw=2, color='green'))
                ax3.text(x - 1, y - 0.5, '满足', ha='center', va='center', fontsize=8, color='green')
                # 不满足条件的循环箭头
                ax3.annotate('', xy=(7.5, y), xytext=(6.5, y),
                           arrowprops=dict(arrowstyle='->', lw=2, color='red'))
                ax3.text(7, y + 0.3, '不满足', ha='center', va='center', fontsize=8, color='red')
                ax3.annotate('', xy=(7.5, 7.5), xytext=(7.5, y),
                           arrowprops=dict(arrowstyle='->', lw=2, color='red'))
                ax3.annotate('', xy=(6.5, 7.5), xytext=(7.5, 7.5),
                           arrowprops=dict(arrowstyle='->', lw=2, color='red'))
            elif i == 7:
                ax3.annotate('', xy=(x, y - 0.4), xytext=(x, y - 0.6),
                           arrowprops=dict(arrowstyle='->', lw=2, color='black'))
                # 继续生长的循环箭头
                ax3.annotate('', xy=(2.5, y), xytext=(3.5, y),
                           arrowprops=dict(arrowstyle='->', lw=2, color='blue'))
                ax3.text(3, y + 0.3, '继续生长', ha='center', va='center', fontsize=8, color='blue')
                ax3.annotate('', xy=(2.5, 7.5), xytext=(2.5, y),
                           arrowprops=dict(arrowstyle='->', lw=2, color='blue'))
                ax3.annotate('', xy=(3.5, 7.5), xytext=(2.5, 7.5),
                           arrowprops=dict(arrowstyle='->', lw=2, color='blue'))
        
        # 算法参数说明
        ax3.text(8.5, 9, "算法参数:\n• 颜色阈值: 30\n• 连通性: 8邻域\n• 最小区域: 100像素\n• 最大区域: 10000像素", 
                fontsize=9, ha='left', va='top',
                bbox=dict(boxstyle="round,pad=0.3", facecolor='lightcyan', alpha=0.8))
        
        return fig3
    
    def save_all_complete_flowcharts(self):
        """保存所有完整的流程图"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 主流程图
        self.create_complete_flowchart()
        main_filename = f"完整智能气吹系统流程图_{timestamp}.png"
        self.fig.savefig(main_filename, dpi=300, bbox_inches='tight')
        
        # 螺旋搜索详细流程图
        fig2 = self.create_spiral_search_detail()
        spiral_filename = f"螺旋搜索算法详细流程图_{timestamp}.png"
        fig2.savefig(spiral_filename, dpi=300, bbox_inches='tight')
        
        # 区域生长详细流程图
        fig3 = self.create_region_growing_detail()
        region_filename = f"区域生长算法详细流程图_{timestamp}.png"
        fig3.savefig(region_filename, dpi=300, bbox_inches='tight')
        
        print(f"📊 完整流程图已保存:")
        print(f"  🎯 主流程图: {main_filename}")
        print(f"  🌀 螺旋搜索图: {spiral_filename}")
        print(f"  🌱 区域生长图: {region_filename}")
        
        # 显示图表
        plt.show()
        
        return main_filename, spiral_filename, region_filename


def main():
    """主函数"""
    print("📊 完整智能气吹控制系统算法流程图生成器")
    print("🎯 包含螺旋搜索算法和区域生长算法")
    print("=" * 60)
    
    try:
        # 创建流程图生成器
        flowchart = CompleteAlgorithmFlowchart()
        
        # 生成并保存所有流程图
        main_file, spiral_file, region_file = flowchart.save_all_complete_flowcharts()
        
        print(f"\n✅ 完整算法流程图生成完成!")
        print(f"🎉 包含螺旋搜索、区域生长、CNN预测、智能控制!")
        
    except Exception as e:
        print(f"❌ 流程图生成失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
